<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>修复版本 - 左右布局编辑器 (Vditor整合版)</title>
    
    <!-- 保留所有必要的库 -->
    <link rel="stylesheet" href="../../../lib/ol3/ol.css">
    <link rel="stylesheet" href="../../../lib/layui/css/layui.css">
    <link rel="stylesheet" href="../../css/ddgc.css">
    <script src="../../../lib/jquery/jquery-2.1.4.min.js"></script>
    <script src="../../../lib/ol3/ol.js"></script>
    <script src="../../../lib/layui/layui.all.js"></script>
    
    <!-- 修复后的 Vditor 引入方式 -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/vditor@3.11.1/dist/index.css" />
    <script>
        // Vditor 加载状态管理
        window.VditorLoadState = {
            loaded: false,
            loading: false,
            error: null
        };

        // 多CDN源加载Vditor
        function loadVditorWithFallback() {
            if (window.VditorLoadState.loading) return;
            window.VditorLoadState.loading = true;
            
            return new Promise((resolve, reject) => {
                const cdnSources = [
                    'https://cdn.jsdelivr.net/npm/vditor@3.11.1/dist/index.min.js',
                    'https://unpkg.com/vditor@3.11.1/dist/index.min.js',
                    'https://cdn.bootcdn.net/ajax/libs/vditor/3.11.1/index.min.js'
                ];
                
                let currentIndex = 0;
                
                function tryLoadFromCDN(index) {
                    if (index >= cdnSources.length) {
                        window.VditorLoadState.error = 'All CDN sources failed';
                        reject(new Error('Failed to load Vditor from all CDN sources'));
                        return;
                    }
                    
                    const script = document.createElement('script');
                    script.src = cdnSources[index];
                    script.onload = () => {
                        console.log(`Vditor loaded from CDN ${index + 1}: ${cdnSources[index]}`);
                        window.VditorLoadState.loaded = true;
                        window.VditorLoadState.loading = false;
                        resolve();
                    };
                    script.onerror = () => {
                        console.warn(`CDN ${index + 1} failed: ${cdnSources[index]}`);
                        tryLoadFromCDN(index + 1);
                    };
                    document.head.appendChild(script);
                }
                
                tryLoadFromCDN(0);
            });
        }
        
        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            loadVditorWithFallback().catch(error => {
                console.error('Vditor loading failed:', error);
                window.VditorLoadState.error = error.message;
                // 显示友好的错误提示
                if (typeof layer !== 'undefined') {
                    layer.msg('编辑器组件加载失败，请检查网络连接或刷新页面重试', {time: 5000});
                }
            });
        });
    </script>
    
    <script src="../../../config/config.js"></script>
    <script src="../../../js/common/common.js"></script>
    <script language="JavaScript" type="text/javascript" src="../../../lib/doSM3/sm3.js"></script>
    <script language="JavaScript" type="text/javascript" src="../../../lib/doSM3/sm4.js"></script>
    <script language="JavaScript" type="text/javascript" src="../../../lib/doSM3/smutils.js"></script>
    <script language="JavaScript" type="text/javascript" src="../../../lib/doSM3/byte.js"></script>
    <script src="../../../lib/jwt/jwt.js"></script>
    <script src="../../../lib/md5/md5.js"></script>
    <script src="../../../ddgc/js/changliang.js"></script>
    <script src="../../../ddgc/js/axios.min.js"></script>

    <style>
        /* 基础样式重置 */
        body {
            margin: 0;
            padding: 0;
            display: flex;
            height: 100vh;
            width: 100vw;
            overflow: hidden;
            color: #333;
            line-height: 1.5;
            font-family: "Microsoft YaHei", "Segoe UI", Arial, sans-serif !important;
        }

        /* 左侧菜单容器 */
        .left-menu {
            width: 250px;
            min-width: 250px;
            max-width: 250px;
            background: #f8f9fa;
            border-right: 1px solid #dee2e6;
            overflow-y: auto;
            position: fixed;
            height: 100vh;
            top: 0;
            left: 0;
            z-index: 10;
            box-sizing: border-box;
            display: flex;
            flex-direction: column;
            font-family: "Microsoft YaHei", "Segoe UI", Arial, sans-serif !important;
        }

        /* 右侧编辑器区域 */
        .right-editor {
            flex: 1;
            display: flex;
            flex-direction: column;
            margin-left: 250px;
            height: 100vh;
            overflow-y: auto;
            background-color: #f8f9fa;
            font-family: Arial, sans-serif !important;
        }

        .editor-content {
            flex: 1;
            padding: 20px;
            box-sizing: border-box;
        }

        /* 按钮样式 */
        .layui-btn {
            display: inline-block;
            padding: 6px 12px;
            margin-bottom: 0;
            font-size: 14px;
            font-weight: 400;
            line-height: 1.5;
            text-align: center;
            white-space: nowrap;
            vertical-align: middle;
            cursor: pointer;
            background-image: none;
            border: 1px solid transparent;
            border-radius: 4px;
            color: #fff;
            background-color: #0F82C5;
            border-color: #0d6ea3;
            transition: all 0.2s ease;
        }

        .layui-btn:hover {
            background-color: #0d6ea3;
            border-color: #0b5d8a;
        }

        /* Vditor 特定样式 */
        #contentArea.vditor-container {
            padding: 0;
            box-shadow: none;
        }

        .vditor {
            flex: 1;
            border-radius: 4px;
            border: 1px solid #dcdfe6;
        }

        .preview-area {
            flex: 1;
            background-color: #fff;
            padding: 20px;
            border-radius: 4px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            overflow-y: auto;
            border: 1px solid #dcdfe6;
        }

        /* 错误提示样式 */
        .error-message {
            background-color: #f8d7da;
            color: #721c24;
            padding: 15px;
            border: 1px solid #f5c6cb;
            border-radius: 4px;
            margin: 20px;
        }

        .success-message {
            background-color: #d4edda;
            color: #155724;
            padding: 15px;
            border: 1px solid #c3e6cb;
            border-radius: 4px;
            margin: 20px;
        }
    </style>
</head>
<body>
<div class="left-menu">
    <div style="padding: 20px;">
        <h3>文档章节</h3>
        <p>这是修复版本的演示</p>
    </div>
</div>

<div class="right-editor">
    <div class="editor-content" id="editor-content">
        <div style="margin-bottom: 20px;">
            <button id="editBtn" class="layui-btn">编辑</button>
            <button id="saveBtn" class="layui-btn" style="display: none;">保存</button>
            <button id="cancelBtn" class="layui-btn" style="display: none;">取消</button>
            <button id="testBtn" class="layui-btn">测试Vditor加载</button>
        </div>
        <div id="contentArea">
            <div id="default-prompt" style="text-align: center; margin-top: 20px; color: #888;">
                <h3>Vditor编辑器修复版本</h3>
                <p>点击"编辑"按钮测试编辑器功能</p>
                <p>点击"测试Vditor加载"按钮检查组件状态</p>
            </div>
        </div>
    </div>
</div>

<script type="application/javascript">
    layui.use(['form', 'layer'], function () {
        var form = layui.form,
            layer = layui.layer,
            $ = layui.$;

        var vditorInstance = null;
        var isEditMode = false;
        var currentMarkdownContent = '# 测试内容\n\n这是一个测试的Markdown内容。\n\n- 列表项1\n- 列表项2\n\n**粗体文本** 和 *斜体文本*';

        // 增强的编辑器初始化函数
        function enterEditMode() {
            isEditMode = true;
            $('#editBtn').hide();
            $('#saveBtn, #cancelBtn').show();
            $('#contentArea').html('');

            // 增强的Vditor检查和重试机制
            function checkVditorAvailability(retryCount = 0, maxRetries = 5) {
                console.log(`Checking Vditor availability... Attempt ${retryCount + 1}/${maxRetries}`);
                
                // 检查全局加载状态
                if (window.VditorLoadState && window.VditorLoadState.error) {
                    showVditorError('Vditor 组件加载失败: ' + window.VditorLoadState.error);
                    return;
                }
                
                // 检查 Vditor 是否已加载
                if (!window.Vditor) {
                    if (retryCount < maxRetries) {
                        console.warn(`Vditor not found, retrying... (${retryCount + 1}/${maxRetries})`);
                        setTimeout(() => checkVditorAvailability(retryCount + 1, maxRetries), 1000);
                        return;
                    } else {
                        showVditorError('Vditor 组件未加载，请刷新页面重试');
                        return;
                    }
                }

                // 检查 Vditor 的关键方法 - 只检查必要的方法
                if (typeof window.Vditor !== 'function') {
                    if (retryCount < maxRetries) {
                        console.warn(`Vditor constructor not ready, retrying... (${retryCount + 1}/${maxRetries})`);
                        setTimeout(() => checkVditorAvailability(retryCount + 1, maxRetries), 1000);
                        return;
                    } else {
                        showVditorError('Vditor 构造函数不可用，请联系管理员');
                        return;
                    }
                }

                // 检查preview方法（编辑器预览必需）
                if (typeof window.Vditor.preview !== 'function') {
                    if (retryCount < maxRetries) {
                        console.warn(`Vditor.preview not ready, retrying... (${retryCount + 1}/${maxRetries})`);
                        setTimeout(() => checkVditorAvailability(retryCount + 1, maxRetries), 1000);
                        return;
                    } else {
                        showVditorError('Vditor 预览功能不可用，请联系管理员');
                        return;
                    }
                }

                // 检查通过，初始化编辑器
                console.log('Vditor check passed. Version:', window.Vditor.version, '. Initializing editor...');
                initializeVditorEditor();
            }

            function showVditorError(message) {
                console.error('Vditor initialization failed:', message);
                $('#contentArea').html(`<div class="error-message">${message}</div>`);
                layer.msg(message, {time: 5000});
                
                // 恢复按钮状态
                $('#editBtn').show();
                $('#saveBtn, #cancelBtn').hide();
                isEditMode = false;
            }

            function initializeVditorEditor() {
                try {
                    vditorInstance = new window.Vditor('contentArea', {
                        height: '500px',
                        placeholder: '请在此处输入 Markdown 内容...',
                        cache: { enable: false },
                        value: currentMarkdownContent,
                        mode: 'wysiwyg',
                        preview: {
                            markdown: { sanitize: true }
                        },
                        toolbar: [
                            'undo', 'redo', '|', 'bold', 'italic', 'strike', 'link', '|',
                            'check', 'outdent', 'indent', '|', 'quote', 'line', 'code',
                            'inline-code', 'insert-before', 'insert-after', '|',
                            'table', '|', 'export'
                        ],
                        after: () => {
                            console.log('Vditor initialized successfully');
                            $('#contentArea').addClass('vditor-container');
                            layer.msg('编辑器初始化成功', {time: 2000});
                        }
                    });
                } catch (error) {
                    console.error('Error initializing Vditor:', error);
                    showVditorError('编辑器初始化失败: ' + error.message);
                }
            }

            // 开始检查
            checkVditorAvailability();
        }

        function exitEditMode() {
            isEditMode = false;
            if (vditorInstance) {
                vditorInstance.destroy();
                vditorInstance = null;
            }
            $('#editBtn').show();
            $('#saveBtn, #cancelBtn').hide();
            renderPreviewMode();
        }

        function saveContent() {
            if (!isEditMode || !vditorInstance || typeof vditorInstance.getValue !== 'function') {
                layer.msg('无法保存内容，请重新进入编辑模式');
                return;
            }

            try {
                var newContent = vditorInstance.getValue();
                currentMarkdownContent = newContent;
                layer.msg('保存成功!');
                exitEditMode();
            } catch (error) {
                console.error('保存失败:', error);
                layer.msg('保存失败: ' + error.message);
            }
        }

        function renderPreviewMode() {
            $('#contentArea').html('').removeClass('vditor-container');
            const previewContainer = $('<div class="preview-area"></div>').appendTo('#contentArea');

            if (currentMarkdownContent && window.Vditor && window.Vditor.preview) {
                Vditor.preview(previewContainer[0], currentMarkdownContent, {
                    hljs: {style: 'github'},
                    markdown: { sanitize: true }
                });
            } else {
                previewContainer.html('<p style="text-align:center;padding:20px;">预览内容加载中...</p>');
            }
        }

        function testVditorStatus() {
            let status = '';
            
            if (window.VditorLoadState) {
                status += `加载状态: ${window.VditorLoadState.loaded ? '已加载' : '未加载'}\n`;
                status += `加载中: ${window.VditorLoadState.loading ? '是' : '否'}\n`;
                if (window.VditorLoadState.error) {
                    status += `错误: ${window.VditorLoadState.error}\n`;
                }
            }
            
            if (window.Vditor) {
                status += `Vditor版本: ${window.Vditor.version || '未知'}\n`;
                status += `html2md方法: ${typeof window.Vditor.html2md === 'function' ? '可用' : '不可用'}\n`;
                status += `preview方法: ${typeof window.Vditor.preview === 'function' ? '可用' : '不可用'}\n`;
            } else {
                status += 'Vditor: 未加载\n';
            }
            
            layer.alert(status, {title: 'Vditor状态检查'});
        }

        // 事件绑定
        $('#editBtn').on('click', enterEditMode);
        $('#saveBtn').on('click', saveContent);
        $('#cancelBtn').on('click', exitEditMode);
        $('#testBtn').on('click', testVditorStatus);

        // 初始化预览模式
        renderPreviewMode();
    });
</script>
</body>
</html>
