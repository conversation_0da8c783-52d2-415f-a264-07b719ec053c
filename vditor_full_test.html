<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Vditor完整版本测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-section {
            border: 1px solid #ddd;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
        }
        .result-area {
            background: #f5f5f5;
            padding: 15px;
            border-radius: 4px;
            margin: 10px 0;
            white-space: pre-wrap;
            font-family: monospace;
            max-height: 400px;
            overflow-y: auto;
        }
        button {
            background: #007cba;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #005a87;
        }
        .success {
            background: #d4edda;
            color: #155724;
            padding: 15px;
            border-radius: 4px;
            margin: 10px 0;
            border-left: 4px solid #28a745;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            padding: 15px;
            border-radius: 4px;
            margin: 10px 0;
            border-left: 4px solid #dc3545;
        }
    </style>
</head>
<body>
    <h1>Vditor完整版本和html2md测试</h1>
    
    <div class="test-section">
        <h2>🎯 测试策略</h2>
        <p>尝试不同的CDN源和加载方式来获取包含html2md方法的完整版本：</p>
        <button onclick="testCDN('jsdelivr-full')">测试 jsDelivr 完整版</button>
        <button onclick="testCDN('unpkg-full')">测试 unpkg 完整版</button>
        <button onclick="testCDN('jsdelivr-dev')">测试 jsDelivr 开发版</button>
        <button onclick="testCDN('github-direct')">测试 GitHub 直链</button>
        <button onclick="testCDN('official')">测试 官方CDN</button>
    </div>
    
    <div class="test-section">
        <h2>📊 测试结果</h2>
        <div id="testResult" class="result-area">点击测试按钮查看结果...</div>
    </div>
    
    <div class="test-section">
        <h2>🔧 备用方案</h2>
        <button onclick="testTurndown()">测试 Turndown.js (专业HTML转MD库)</button>
        <button onclick="testCustomConverter()">测试 自定义转换器</button>
    </div>

    <script>
        const cdnConfigs = {
            'jsdelivr-full': {
                css: 'https://cdn.jsdelivr.net/npm/vditor/dist/index.css',
                js: 'https://cdn.jsdelivr.net/npm/vditor/dist/index.js', // 使用.js而不是.min.js
                name: 'jsDelivr 完整版'
            },
            'unpkg-full': {
                css: 'https://unpkg.com/vditor/dist/index.css',
                js: 'https://unpkg.com/vditor/dist/index.js',
                name: 'unpkg 完整版'
            },
            'jsdelivr-dev': {
                css: 'https://cdn.jsdelivr.net/npm/vditor@latest/dist/index.css',
                js: 'https://cdn.jsdelivr.net/npm/vditor@latest/dist/index.js',
                name: 'jsDelivr 最新开发版'
            },
            'github-direct': {
                css: 'https://raw.githubusercontent.com/Vanessa219/vditor/master/dist/index.css',
                js: 'https://raw.githubusercontent.com/Vanessa219/vditor/master/dist/index.js',
                name: 'GitHub 直链'
            },
            'official': {
                css: 'https://vditor.b3log.org/dist/index.css',
                js: 'https://vditor.b3log.org/dist/index.js',
                name: '官方CDN'
            }
        };

        function clearVditor() {
            // 移除现有的Vditor脚本和样式
            document.querySelectorAll('script[src*="vditor"], link[href*="vditor"]').forEach(el => el.remove());
            
            // 清除Vditor对象
            if (window.Vditor) {
                delete window.Vditor;
            }
        }

        function testCDN(configKey) {
            const config = cdnConfigs[configKey];
            if (!config) return;

            document.getElementById('testResult').innerHTML = `正在测试 ${config.name}...\n请稍候...`;
            
            clearVditor();
            
            // 加载CSS
            const link = document.createElement('link');
            link.rel = 'stylesheet';
            link.href = config.css;
            document.head.appendChild(link);
            
            // 加载JS
            const script = document.createElement('script');
            script.src = config.js;
            script.onload = () => {
                setTimeout(() => {
                    testVditorMethods(config.name);
                }, 1000);
            };
            script.onerror = () => {
                document.getElementById('testResult').innerHTML = `❌ ${config.name} 加载失败`;
            };
            
            document.head.appendChild(script);
        }

        function testVditorMethods(sourceName) {
            let result = `=== ${sourceName} 测试结果 ===\n\n`;
            
            if (window.Vditor) {
                result += `✅ Vditor对象存在\n`;
                result += `📦 版本: ${window.Vditor.version || '未知'}\n`;
                result += `🔧 构造函数: ${typeof window.Vditor}\n\n`;
                
                // 检查html2md方法
                if (typeof window.Vditor.html2md === 'function') {
                    result += `🎉 找到html2md方法！\n`;
                    
                    try {
                        const testHtml = `
                            <h1>测试标题</h1>
                            <p><strong>粗体文本</strong>和<em>斜体文本</em></p>
                            <ul>
                                <li>列表项1</li>
                                <li>列表项2</li>
                            </ul>
                            <blockquote>这是引用</blockquote>
                        `;
                        
                        const markdown = window.Vditor.html2md(testHtml);
                        result += `✅ html2md测试成功！\n\n`;
                        result += `📝 测试HTML:\n${testHtml}\n\n`;
                        result += `📝 转换结果:\n${markdown}\n\n`;
                        result += `🎯 这个版本可以使用！\n`;
                        
                        // 显示成功消息
                        document.getElementById('testResult').innerHTML = result;
                        document.getElementById('testResult').className = 'result-area success';
                        return;
                        
                    } catch (error) {
                        result += `❌ html2md调用失败: ${error.message}\n`;
                    }
                } else {
                    result += `❌ html2md方法不存在\n`;
                    
                    // 列出所有可用方法
                    const methods = Object.getOwnPropertyNames(window.Vditor);
                    result += `\n📋 可用方法 (${methods.length}个):\n`;
                    methods.forEach(method => {
                        if (typeof window.Vditor[method] === 'function') {
                            result += `  - ${method}()\n`;
                        }
                    });
                }
            } else {
                result += `❌ Vditor对象不存在\n`;
            }
            
            document.getElementById('testResult').innerHTML = result;
            document.getElementById('testResult').className = 'result-area error';
        }

        function testTurndown() {
            document.getElementById('testResult').innerHTML = '正在加载 Turndown.js...\n请稍候...';
            
            // 加载Turndown.js
            const script = document.createElement('script');
            script.src = 'https://cdn.jsdelivr.net/npm/turndown/dist/turndown.js';
            script.onload = () => {
                try {
                    const turndownService = new TurndownService();
                    
                    const testHtml = `
                        <h1>测试标题</h1>
                        <p><strong>粗体文本</strong>和<em>斜体文本</em></p>
                        <ul>
                            <li>列表项1</li>
                            <li>列表项2</li>
                        </ul>
                        <blockquote>这是引用</blockquote>
                    `;
                    
                    const markdown = turndownService.turndown(testHtml);
                    
                    let result = `=== Turndown.js 测试结果 ===\n\n`;
                    result += `✅ Turndown.js 加载成功\n`;
                    result += `🎉 HTML转Markdown测试成功！\n\n`;
                    result += `📝 测试HTML:\n${testHtml}\n\n`;
                    result += `📝 转换结果:\n${markdown}\n\n`;
                    result += `💡 可以使用Turndown.js作为备用方案！\n`;
                    
                    document.getElementById('testResult').innerHTML = result;
                    document.getElementById('testResult').className = 'result-area success';
                    
                } catch (error) {
                    document.getElementById('testResult').innerHTML = `❌ Turndown.js 测试失败: ${error.message}`;
                    document.getElementById('testResult').className = 'result-area error';
                }
            };
            script.onerror = () => {
                document.getElementById('testResult').innerHTML = '❌ Turndown.js 加载失败';
                document.getElementById('testResult').className = 'result-area error';
            };
            
            document.head.appendChild(script);
        }

        function testCustomConverter() {
            const testHtml = `
                <h1>测试标题</h1>
                <h2>二级标题</h2>
                <p><strong>粗体文本</strong>和<em>斜体文本</em></p>
                <ul>
                    <li>列表项1</li>
                    <li>列表项2</li>
                </ul>
                <blockquote>这是引用</blockquote>
                <p>普通段落文本</p>
            `;
            
            // 简单的HTML转Markdown函数
            function simpleHtml2Md(html) {
                return html
                    .replace(/<h1[^>]*>(.*?)<\/h1>/gi, '# $1\n')
                    .replace(/<h2[^>]*>(.*?)<\/h2>/gi, '## $1\n')
                    .replace(/<h3[^>]*>(.*?)<\/h3>/gi, '### $1\n')
                    .replace(/<strong[^>]*>(.*?)<\/strong>/gi, '**$1**')
                    .replace(/<b[^>]*>(.*?)<\/b>/gi, '**$1**')
                    .replace(/<em[^>]*>(.*?)<\/em>/gi, '*$1*')
                    .replace(/<i[^>]*>(.*?)<\/i>/gi, '*$1*')
                    .replace(/<li[^>]*>(.*?)<\/li>/gi, '- $1\n')
                    .replace(/<\/?(ul|ol)[^>]*>/gi, '\n')
                    .replace(/<blockquote[^>]*>(.*?)<\/blockquote>/gi, '> $1\n')
                    .replace(/<p[^>]*>(.*?)<\/p>/gi, '$1\n\n')
                    .replace(/<br[^>]*>/gi, '\n')
                    .replace(/<[^>]*>/g, '')
                    .replace(/\n\s*\n\s*\n/g, '\n\n')
                    .trim();
            }
            
            const markdown = simpleHtml2Md(testHtml);
            
            let result = `=== 自定义转换器测试结果 ===\n\n`;
            result += `✅ 自定义转换器测试成功\n\n`;
            result += `📝 测试HTML:\n${testHtml}\n\n`;
            result += `📝 转换结果:\n${markdown}\n\n`;
            result += `💡 可以使用增强的自定义转换器作为备用方案！\n`;
            
            document.getElementById('testResult').innerHTML = result;
            document.getElementById('testResult').className = 'result-area success';
        }
    </script>
</body>
</html>
