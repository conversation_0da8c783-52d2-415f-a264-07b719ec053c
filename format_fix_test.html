<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>格式修复测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-section {
            border: 1px solid #ddd;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
        }
        .result-area {
            background: #f5f5f5;
            padding: 15px;
            border-radius: 4px;
            margin: 10px 0;
            white-space: pre-wrap;
            font-family: monospace;
            max-height: 400px;
            overflow-y: auto;
        }
        button {
            background: #007cba;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #005a87;
        }
        .problem-sample {
            background: #ffe6e6;
            padding: 15px;
            border-radius: 4px;
            margin: 10px 0;
            border-left: 4px solid #ff4444;
        }
    </style>
</head>
<body>
    <h1>Word格式重复问题修复测试</h1>
    
    <div class="test-section">
        <h2>🐛 问题样本</h2>
        <p>以下是模拟您Word文档中可能出现的嵌套格式问题：</p>
        
        <div class="problem-sample">
            <h3>嵌套粗体问题示例：</h3>
            <div id="problemHtml" style="font-family: monospace; font-size: 12px; background: white; padding: 10px; border-radius: 4px;">
                &lt;strong&gt;&lt;strong&gt;1.1 编制依据和适用范围&lt;/strong&gt;&lt;/strong&gt;<br>
                &lt;b&gt;&lt;strong&gt;重要标题&lt;/strong&gt;&lt;/b&gt;<br>
                &lt;p&gt;&lt;strong&gt;&lt;b&gt;嵌套粗体文本&lt;/b&gt;&lt;/strong&gt;&lt;/p&gt;<br>
                &lt;em&gt;&lt;i&gt;嵌套斜体文本&lt;/i&gt;&lt;/em&gt;
            </div>
        </div>
        
        <button onclick="testFormatFix()">🔧 测试格式修复</button>
        <button onclick="testRealWordContent()">📄 测试真实Word内容</button>
    </div>
    
    <div class="test-section">
        <h2>🔧 修复结果</h2>
        <div id="fixResult" class="result-area">点击测试按钮查看修复结果...</div>
    </div>
    
    <div class="test-section">
        <h2>📊 修复规则说明</h2>
        <ul>
            <li><strong>嵌套粗体清理</strong>：<code>&lt;strong&gt;&lt;strong&gt;文本&lt;/strong&gt;&lt;/strong&gt;</code> → <code>**文本**</code></li>
            <li><strong>混合粗体标签</strong>：<code>&lt;b&gt;&lt;strong&gt;文本&lt;/strong&gt;&lt;/b&gt;</code> → <code>**文本**</code></li>
            <li><strong>重复标记清理</strong>：<code>****文本****</code> → <code>**文本**</code></li>
            <li><strong>空标记移除</strong>：<code>** **</code> → 删除</li>
            <li><strong>标记间距修复</strong>：<code>** 文本 **</code> → <code>**文本**</code></li>
        </ul>
    </div>

    <script>
        // 复制修复后的转换函数
        function htmlToMarkdownManual(html) {
            console.log('开始手动转换，输入长度:', html.length);
            
            let result = html;
            
            // 第一步：预处理，移除嵌套的相同标签
            result = result
                // 移除嵌套的粗体标签
                .replace(/<(strong|b)[^>]*>\s*<(strong|b)[^>]*>(.*?)<\/\2>\s*<\/\1>/gi, '<$1>$3</$1>')
                // 移除嵌套的斜体标签
                .replace(/<(em|i)[^>]*>\s*<(em|i)[^>]*>(.*?)<\/\2>\s*<\/\1>/gi, '<$1>$3</$1>')
                // 清理空的格式标签
                .replace(/<(strong|b|em|i)[^>]*>\s*<\/\1>/gi, '');
            
            // 第二步：标题转换
            result = result
                .replace(/<h1[^>]*>(.*?)<\/h1>/gi, '\n# $1\n')
                .replace(/<h2[^>]*>(.*?)<\/h2>/gi, '\n## $1\n')
                .replace(/<h3[^>]*>(.*?)<\/h3>/gi, '\n### $1\n')
                .replace(/<h4[^>]*>(.*?)<\/h4>/gi, '\n#### $1\n')
                .replace(/<h5[^>]*>(.*?)<\/h5>/gi, '\n##### $1\n')
                .replace(/<h6[^>]*>(.*?)<\/h6>/gi, '\n###### $1\n');
            
            // 第三步：文本格式转换
            result = result
                .replace(/<(strong|b)[^>]*>(.*?)<\/\1>/gi, '**$2**')
                .replace(/<(em|i)[^>]*>(.*?)<\/\1>/gi, '*$2*');
            
            // 第四步：结构元素转换
            result = result
                .replace(/<blockquote[^>]*>(.*?)<\/blockquote>/gi, '\n> $1\n')
                .replace(/<li[^>]*>(.*?)<\/li>/gi, '- $1\n')
                .replace(/<\/?(ul|ol)[^>]*>/gi, '\n')
                .replace(/<p[^>]*>(.*?)<\/p>/gi, '\n$1\n')
                .replace(/<br[^>]*>/gi, '\n')
                .replace(/<a[^>]*href="([^"]*)"[^>]*>(.*?)<\/a>/gi, '[$2]($1)')
                .replace(/<code[^>]*>(.*?)<\/code>/gi, '`$1`');
            
            // 第五步：移除剩余的HTML标签
            result = result.replace(/<[^>]*>/g, '');
            
            // 第六步：清理格式问题
            result = result
                // 修复重复的粗体标记
                .replace(/\*\*+/g, '**')
                .replace(/\*\*\s*\*\*/g, '**')
                // 修复重复的斜体标记
                .replace(/\*+(?!\*)/g, '*')
                .replace(/\*\s*\*(?!\*)/g, '*')
                // 修复空的格式标记
                .replace(/\*\*\s*\*\*/g, '')
                .replace(/\*\s*\*/g, '')
                // 清理多余的换行
                .replace(/\n\s*\n\s*\n/g, '\n\n')
                .replace(/\n\s+/g, '\n')
                .replace(/^\s+|\s+$/g, '')
                .trim();
            
            console.log('手动转换完成，输出长度:', result.length);
            return result;
        }

        function testFormatFix() {
            const problemHtml = `
                <strong><strong>1.1 编制依据和适用范围</strong></strong>
                <b><strong>重要标题</strong></b>
                <p><strong><b>嵌套粗体文本</b></strong></p>
                <em><i>嵌套斜体文本</i></em>
                <p><strong></strong>空粗体标签</p>
                <p><b>正常粗体</b>和<i>正常斜体</i></p>
            `;
            
            console.log('测试格式修复...');
            console.log('原始HTML:', problemHtml);
            
            const result = htmlToMarkdownManual(problemHtml);
            
            document.getElementById('fixResult').innerHTML = 
                '=== 格式修复测试结果 ===\n\n' +
                '原始HTML:\n' + problemHtml + '\n\n' +
                '修复后的Markdown:\n' + result + '\n\n' +
                '=== 期望结果 ===\n' +
                '**1.1 编制依据和适用范围**\n' +
                '**重要标题**\n' +
                '**嵌套粗体文本**\n' +
                '*嵌套斜体文本*\n' +
                '空粗体标签\n' +
                '**正常粗体**和*正常斜体*';
        }

        function testRealWordContent() {
            // 模拟真实Word文档的复杂HTML结构
            const realWordHtml = `
                <html xmlns:o="urn:schemas-microsoft-com:office:office">
                <head></head>
                <body>
                    <p class="MsoNormal"><strong><span style="font-family:宋体">1、原则和依据</span></strong></p>
                    <p class="MsoNormal"><strong><span style="font-family:宋体">1.1 编制依据和适用范围</span></strong></p>
                    <p class="MsoNormal"><span style="font-family:宋体">《2024年安徽电网稳定运行规定》（以下简称"本规定"）针对2024年中安徽电网接线方式，依据</span><em><span style="font-family:宋体">《电力系统安全稳定导则》</span></em><span style="font-family:宋体">（GB 38755-2019）、</span><strong><span style="font-family:宋体">《电力系统安全稳定计算规范》</span></strong><span style="font-family:宋体">（GB/T 40581-2021）等制定。</span></p>
                    <p class="MsoNormal"><span style="font-family:宋体">本规定给出安徽省调（以下简称"省调"）调度管辖范围内电网的安全稳定运行要求。</span></p>
                </body>
                </html>
            `;
            
            console.log('测试真实Word内容...');
            
            // 先进行基本清洗
            let cleaned = realWordHtml;
            const bodyMatch = cleaned.match(/<body[^>]*>([\s\S]*)<\/body>/i);
            if (bodyMatch) {
                cleaned = bodyMatch[1];
            }
            
            cleaned = cleaned
                .replace(/<\/?\w+:[^>]*>/g, '')
                .replace(/\sstyle="[^"]*"/gi, '')
                .replace(/\sclass="[^"]*"/gi, '')
                .replace(/<!--[\s\S]*?-->/g, '')
                .replace(/\s+/g, ' ')
                .trim();
            
            const result = htmlToMarkdownManual(cleaned);
            
            document.getElementById('fixResult').innerHTML = 
                '=== 真实Word内容测试结果 ===\n\n' +
                '清洗后HTML:\n' + cleaned + '\n\n' +
                '转换后的Markdown:\n' + result;
        }
    </script>
</body>
</html>
