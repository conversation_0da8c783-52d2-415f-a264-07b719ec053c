<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Vditor简化修复版本</title>
    
    <!-- 使用稳定的CDN源 -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/vditor@3.11.1/dist/index.css" />
    <script src="https://cdn.jsdelivr.net/npm/vditor@3.11.1/dist/index.min.js"></script>
    
    <!-- 引入layui用于消息提示 -->
    <link rel="stylesheet" href="https://unpkg.com/layui@2.6.8/dist/css/layui.css">
    <script src="https://unpkg.com/layui@2.6.8/dist/layui.js"></script>

    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: "Microsoft YaHei", Arial, sans-serif;
            background-color: #f5f5f5;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .toolbar {
            margin-bottom: 20px;
            padding: 10px;
            background: #f8f9fa;
            border-radius: 4px;
        }
        
        .layui-btn {
            margin-right: 10px;
        }
        
        #contentArea {
            min-height: 500px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        
        .status-info {
            background: #e7f3ff;
            padding: 15px;
            border-radius: 4px;
            margin-bottom: 20px;
            border-left: 4px solid #409eff;
        }
        
        .error-info {
            background: #fef0f0;
            padding: 15px;
            border-radius: 4px;
            margin-bottom: 20px;
            border-left: 4px solid #f56c6c;
            color: #f56c6c;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Vditor编辑器修复测试</h1>
        
        <div id="statusInfo" class="status-info">
            <strong>状态检查：</strong>
            <div id="statusContent">正在检查Vditor加载状态...</div>
        </div>
        
        <div class="toolbar">
            <button id="checkBtn" class="layui-btn layui-btn-primary">检查状态</button>
            <button id="editBtn" class="layui-btn">开始编辑</button>
            <button id="saveBtn" class="layui-btn layui-btn-warm" style="display: none;">保存</button>
            <button id="cancelBtn" class="layui-btn layui-btn-primary" style="display: none;">取消</button>
            <button id="previewBtn" class="layui-btn layui-btn-normal">预览模式</button>
        </div>
        
        <div id="contentArea">
            <div style="padding: 40px; text-align: center; color: #999;">
                <h3>请点击"开始编辑"按钮初始化编辑器</h3>
                <p>或点击"检查状态"查看Vditor加载情况</p>
            </div>
        </div>
    </div>

    <script>
        // 等待页面和layui加载完成
        document.addEventListener('DOMContentLoaded', function() {
            // 等待layui加载
            if (typeof layui !== 'undefined') {
                layui.use(['layer'], function() {
                    window.layer = layui.layer;
                    initApp();
                });
            } else {
                // 如果layui未加载，使用原生alert
                window.layer = {
                    msg: function(text) { alert(text); },
                    alert: function(text) { alert(text); }
                };
                initApp();
            }
        });

        function initApp() {
            let vditorInstance = null;
            let isEditMode = false;
            const defaultContent = '# 测试文档\n\n这是一个测试的Markdown文档。\n\n## 功能测试\n\n- **粗体文本**\n- *斜体文本*\n- `代码片段`\n\n> 这是一个引用块\n\n```javascript\nconsole.log("Hello, Vditor!");\n```';

            // 检查Vditor状态
            function checkVditorStatus() {
                let status = '';
                let hasError = false;

                if (typeof window.Vditor === 'undefined') {
                    status = '❌ Vditor未加载';
                    hasError = true;
                } else {
                    status = '✅ Vditor已加载\n';
                    
                    // 检查版本
                    if (window.Vditor.version) {
                        status += `📦 版本: ${window.Vditor.version}\n`;
                    } else {
                        status += '⚠️ 版本信息不可用\n';
                    }
                    
                    // 检查关键方法
                    if (typeof window.Vditor === 'function') {
                        status += '✅ 构造函数可用\n';
                    } else {
                        status += '❌ 构造函数不可用\n';
                        hasError = true;
                    }
                    
                    if (typeof window.Vditor.preview === 'function') {
                        status += '✅ preview方法可用\n';
                    } else {
                        status += '❌ preview方法不可用\n';
                        hasError = true;
                    }
                    
                    if (typeof window.Vditor.html2md === 'function') {
                        status += '✅ html2md方法可用\n';
                    } else {
                        status += '⚠️ html2md方法不可用（可选功能）\n';
                    }
                }

                const statusDiv = document.getElementById('statusInfo');
                const statusContent = document.getElementById('statusContent');
                
                if (hasError) {
                    statusDiv.className = 'error-info';
                    statusContent.innerHTML = '<strong>检查结果：</strong><br>' + status.replace(/\n/g, '<br>');
                } else {
                    statusDiv.className = 'status-info';
                    statusContent.innerHTML = '<strong>检查结果：</strong><br>' + status.replace(/\n/g, '<br>');
                }

                return !hasError;
            }

            // 初始化编辑器
            function initEditor() {
                if (!checkVditorStatus()) {
                    layer.msg('Vditor状态检查失败，无法初始化编辑器', {time: 3000});
                    return;
                }

                try {
                    // 清空容器
                    document.getElementById('contentArea').innerHTML = '';
                    
                    // 创建Vditor实例
                    vditorInstance = new Vditor('contentArea', {
                        height: 500,
                        mode: 'wysiwyg',
                        placeholder: '请在此输入Markdown内容...',
                        value: defaultContent,
                        cache: { enable: false },
                        preview: {
                            markdown: { sanitize: true }
                        },
                        toolbar: [
                            'undo', 'redo', '|',
                            'bold', 'italic', 'strike', '|',
                            'line', 'quote', 'list', 'ordered-list', 'check', '|',
                            'code', 'inline-code', '|',
                            'table', 'link'
                        ],
                        after: () => {
                            console.log('Vditor初始化成功');
                            layer.msg('编辑器初始化成功！', {time: 2000});
                            isEditMode = true;
                            updateButtonState();
                        }
                    });
                } catch (error) {
                    console.error('Vditor初始化失败:', error);
                    layer.msg('编辑器初始化失败: ' + error.message, {time: 5000});
                }
            }

            // 保存内容
            function saveContent() {
                if (!vditorInstance) {
                    layer.msg('编辑器未初始化', {time: 2000});
                    return;
                }

                try {
                    const content = vditorInstance.getValue();
                    console.log('保存的内容:', content);
                    layer.msg('内容保存成功！', {time: 2000});
                } catch (error) {
                    console.error('保存失败:', error);
                    layer.msg('保存失败: ' + error.message, {time: 3000});
                }
            }

            // 退出编辑模式
            function exitEditor() {
                if (vditorInstance) {
                    vditorInstance.destroy();
                    vditorInstance = null;
                }
                isEditMode = false;
                updateButtonState();
                
                // 显示预览
                showPreview();
            }

            // 显示预览
            function showPreview() {
                const contentArea = document.getElementById('contentArea');
                contentArea.innerHTML = '<div style="padding: 20px;"><h3>预览模式</h3><p>点击"开始编辑"重新进入编辑模式</p></div>';
                
                // 如果有Vditor.preview方法，使用它来渲染
                if (typeof window.Vditor !== 'undefined' && typeof window.Vditor.preview === 'function') {
                    try {
                        Vditor.preview(contentArea, defaultContent, {
                            markdown: { sanitize: true }
                        });
                    } catch (error) {
                        console.warn('预览渲染失败:', error);
                    }
                }
            }

            // 更新按钮状态
            function updateButtonState() {
                const editBtn = document.getElementById('editBtn');
                const saveBtn = document.getElementById('saveBtn');
                const cancelBtn = document.getElementById('cancelBtn');

                if (isEditMode) {
                    editBtn.style.display = 'none';
                    saveBtn.style.display = 'inline-block';
                    cancelBtn.style.display = 'inline-block';
                } else {
                    editBtn.style.display = 'inline-block';
                    saveBtn.style.display = 'none';
                    cancelBtn.style.display = 'none';
                }
            }

            // 事件绑定
            document.getElementById('checkBtn').addEventListener('click', checkVditorStatus);
            document.getElementById('editBtn').addEventListener('click', initEditor);
            document.getElementById('saveBtn').addEventListener('click', saveContent);
            document.getElementById('cancelBtn').addEventListener('click', exitEditor);
            document.getElementById('previewBtn').addEventListener('click', showPreview);

            // 初始状态检查
            setTimeout(checkVditorStatus, 1000);
        }
    </script>
</body>
</html>
