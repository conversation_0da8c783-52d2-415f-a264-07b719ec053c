<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Word文档粘贴测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-section {
            border: 1px solid #ddd;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
        }
        .sample-word-content {
            background: #f0f8ff;
            padding: 15px;
            border-radius: 4px;
            margin: 10px 0;
            border-left: 4px solid #007cba;
        }
        .result-area {
            background: #f5f5f5;
            padding: 15px;
            border-radius: 4px;
            margin: 10px 0;
            white-space: pre-wrap;
            font-family: monospace;
            max-height: 300px;
            overflow-y: auto;
        }
        button {
            background: #007cba;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #005a87;
        }
        .word-sample {
            font-family: "Times New Roman", serif;
            line-height: 1.6;
        }
    </style>
</head>
<body>
    <h1>Word文档粘贴功能测试</h1>
    
    <div class="test-section">
        <h2>📄 模拟Word文档内容</h2>
        <p>以下内容模拟了Word文档的格式，您可以复制这些内容进行测试：</p>
        
        <div class="sample-word-content word-sample">
            <h1 style="color: #2E74B5; font-weight: bold;">1、原则和依据</h1>
            
            <h2 style="color: #2E74B5; font-weight: bold;">1.1 编制依据和适用范围</h2>
            
            <p style="text-align: justify; line-height: 1.5;">
                <strong>《2024年安徽电网稳定运行规定》</strong>（以下简称"本规定"）针对2024年中安徽电网接线方式，
                依据<em>《电力系统安全稳定导则》</em>（GB 38755-2019）、
                <strong>《电力系统安全稳定计算规范》</strong>（GB/T 40581-2021）等制定。
            </p>
            
            <h2 style="color: #2E74B5; font-weight: bold;">1.2 主要内容</h2>
            
            <ul style="margin-left: 20px;">
                <li><strong>安全稳定标准</strong>：确保电网安全稳定运行</li>
                <li><em>运行方式</em>：规范电网运行方式</li>
                <li>应急处置：建立应急响应机制</li>
            </ul>
            
            <blockquote style="border-left: 3px solid #ccc; padding-left: 15px; margin: 20px 0; font-style: italic;">
                本规定适用于安徽省电力调度控制中心及相关运行单位。
            </blockquote>
        </div>
        
        <button onclick="copyWordContent()">📋 复制Word样式内容</button>
        <button onclick="testCleanFunction()">🧪 测试清洗功能</button>
    </div>
    
    <div class="test-section">
        <h2>🔧 清洗结果</h2>
        <div id="cleanResult" class="result-area">点击"测试清洗功能"查看结果...</div>
    </div>
    
    <div class="test-section">
        <h2>📝 使用说明</h2>
        <ol>
            <li><strong>复制测试内容</strong>：点击"复制Word样式内容"按钮</li>
            <li><strong>粘贴到编辑器</strong>：在您的编辑器中粘贴内容</li>
            <li><strong>观察效果</strong>：查看是否正确保留了标题、粗体、斜体等格式</li>
            <li><strong>本地测试</strong>：点击"测试清洗功能"直接查看清洗效果</li>
        </ol>
        
        <h3>期望的转换效果：</h3>
        <ul>
            <li>标题1 → <code># 标题</code></li>
            <li>标题2 → <code>## 标题</code></li>
            <li>粗体 → <code>**文本**</code></li>
            <li>斜体 → <code>*文本*</code></li>
            <li>列表 → <code>- 项目</code></li>
            <li>引用 → <code>> 引用内容</code></li>
        </ul>
    </div>

    <script>
        // 复制增强的HTML清洗函数
        function cleanHtmlContent(html, fallbackText) {
            try {
                console.log('开始清洗HTML，原始长度:', html.length);
                
                let cleaned = html;
                
                // 提取body内容
                const bodyMatch = cleaned.match(/<body[^>]*>([\s\S]*)<\/body>/i);
                if (bodyMatch) {
                    cleaned = bodyMatch[1];
                    console.log('提取body内容，长度:', cleaned.length);
                }
                
                // 深度清洗
                cleaned = cleaned
                    .replace(/<\/?\w+:[^>]*>/g, '')
                    .replace(/<o:p[\s\S]*?<\/o:p>/gi, '')
                    .replace(/<!\[if[\s\S]*?\]>/gi, '')
                    .replace(/<!\[endif\]>/gi, '')
                    .replace(/\sstyle="[^"]*"/gi, '')
                    .replace(/\sclass="[^"]*"/gi, '')
                    .replace(/\s(lang|xml:lang|xmlns|face|size|color|width|height|align|valign)="[^"]*"/gi, '')
                    .replace(/<!--[\s\S]*?-->/g, '')
                    .replace(/<(p|span|div|font|strong|b|em|i)\b[^>]*>\s*<\/\1>/gi, '')
                    .replace(/\s+/g, ' ')
                    .trim();

                console.log('清洗后长度:', cleaned.length);

                // 手动转换
                const manualResult = htmlToMarkdownManual(cleaned);
                
                if (manualResult && manualResult.trim().length > 0) {
                    console.log('手动转换成功，结果长度:', manualResult.length);
                    return manualResult;
                }
                
                // 结构化文本提取
                return extractStructuredText(cleaned, fallbackText);
                
            } catch (error) {
                console.error('HTML清洗过程出错:', error);
                return fallbackText || '';
            }
        }

        function htmlToMarkdownManual(html) {
            return html
                .replace(/<h1[^>]*>(.*?)<\/h1>/gi, '\n# $1\n')
                .replace(/<h2[^>]*>(.*?)<\/h2>/gi, '\n## $1\n')
                .replace(/<h3[^>]*>(.*?)<\/h3>/gi, '\n### $1\n')
                .replace(/<h4[^>]*>(.*?)<\/h4>/gi, '\n#### $1\n')
                .replace(/<h5[^>]*>(.*?)<\/h5>/gi, '\n##### $1\n')
                .replace(/<h6[^>]*>(.*?)<\/h6>/gi, '\n###### $1\n')
                .replace(/<(strong|b)[^>]*>(.*?)<\/\1>/gi, '**$2**')
                .replace(/<(em|i)[^>]*>(.*?)<\/\1>/gi, '*$2*')
                .replace(/<p[^>]*>(.*?)<\/p>/gi, '\n$1\n')
                .replace(/<br[^>]*>/gi, '\n')
                .replace(/<li[^>]*>(.*?)<\/li>/gi, '- $1\n')
                .replace(/<\/?(ul|ol)[^>]*>/gi, '\n')
                .replace(/<a[^>]*href="([^"]*)"[^>]*>(.*?)<\/a>/gi, '[$2]($1)')
                .replace(/<code[^>]*>(.*?)<\/code>/gi, '`$1`')
                .replace(/<blockquote[^>]*>(.*?)<\/blockquote>/gi, '\n> $1\n')
                .replace(/<[^>]*>/g, '')
                .replace(/\n\s*\n\s*\n/g, '\n\n')
                .replace(/^\s+|\s+$/g, '')
                .trim();
        }

        function extractStructuredText(html, fallbackText) {
            try {
                const tempDiv = document.createElement('div');
                tempDiv.innerHTML = html;
                
                let result = '';
                
                function processNode(node) {
                    if (node.nodeType === 3) {
                        const text = node.textContent.trim();
                        if (text) {
                            result += text + ' ';
                        }
                    } else if (node.nodeType === 1) {
                        const tagName = node.tagName.toLowerCase();
                        
                        switch (tagName) {
                            case 'h1': result += '\n# '; break;
                            case 'h2': result += '\n## '; break;
                            case 'h3': result += '\n### '; break;
                            case 'h4': result += '\n#### '; break;
                            case 'h5': result += '\n##### '; break;
                            case 'h6': result += '\n###### '; break;
                            case 'p': result += '\n\n'; break;
                            case 'br': result += '\n'; return;
                            case 'strong':
                            case 'b': result += '**'; break;
                            case 'em':
                            case 'i': result += '*'; break;
                            case 'li': result += '\n- '; break;
                            case 'blockquote': result += '\n> '; break;
                        }
                        
                        for (let child of node.childNodes) {
                            processNode(child);
                        }
                        
                        switch (tagName) {
                            case 'h1':
                            case 'h2':
                            case 'h3':
                            case 'h4':
                            case 'h5':
                            case 'h6':
                                result += '\n'; break;
                            case 'strong':
                            case 'b': result += '**'; break;
                            case 'em':
                            case 'i': result += '*'; break;
                            case 'p': result += '\n'; break;
                        }
                    }
                }
                
                processNode(tempDiv);
                
                result = result
                    .replace(/\n\s*\n\s*\n/g, '\n\n')
                    .replace(/^\s+|\s+$/g, '')
                    .trim();
                
                return result || fallbackText || '';
            } catch (error) {
                console.error('结构化文本提取失败:', error);
                return fallbackText || '';
            }
        }

        function copyWordContent() {
            const wordContent = document.querySelector('.sample-word-content');
            
            const range = document.createRange();
            range.selectNodeContents(wordContent);
            const selection = window.getSelection();
            selection.removeAllRanges();
            selection.addRange(range);
            
            try {
                document.execCommand('copy');
                alert('Word样式内容已复制到剪贴板！\n现在可以在编辑器中粘贴测试。');
            } catch (err) {
                alert('复制失败，请手动选择并复制内容。');
            }
            
            selection.removeAllRanges();
        }

        function testCleanFunction() {
            const wordContent = document.querySelector('.sample-word-content').innerHTML;
            
            console.log('测试Word内容清洗...');
            const cleaned = cleanHtmlContent(wordContent, '纯文本后备');
            
            document.getElementById('cleanResult').innerHTML = 
                '原始HTML长度: ' + wordContent.length + '\n\n' +
                '清洗后的Markdown:\n' + cleaned;
        }
    </script>
</body>
</html>
