// 针对您原始文件的修复代码
// 请将此代码替换您原文件中第883-893行的错误检查逻辑

// 修复后的检查逻辑 - 不依赖html2md方法
function checkAndInitVditor(retryCount = 0, maxRetries = 3) {
    console.log(`检查Vditor可用性... 尝试 ${retryCount + 1}/${maxRetries}`);
    
    // 检查 Vditor 是否已加载
    if (!window.Vditor) {
        if (retryCount < maxRetries) {
            console.warn(`Vditor未找到，重试中... (${retryCount + 1}/${maxRetries})`);
            setTimeout(() => checkAndInitVditor(retryCount + 1, maxRetries), 1000);
            return;
        } else {
            console.error('多次重试后Vditor仍未加载');
            layer.msg('编辑器组件未加载，请刷新页面重试', {time: 5000});
            restoreButtonState();
            return;
        }
    }

    // 只检查必要的方法 - 移除对html2md的依赖
    if (typeof window.Vditor !== 'function') {
        if (retryCount < maxRetries) {
            console.warn(`Vditor构造函数未就绪，重试中... (${retryCount + 1}/${maxRetries})`);
            setTimeout(() => checkAndInitVditor(retryCount + 1, maxRetries), 1000);
            return;
        } else {
            console.error('Vditor构造函数不可用');
            layer.msg('编辑器构造函数不可用，请联系管理员', {time: 5000});
            restoreButtonState();
            return;
        }
    }

    // 检查preview方法（用于预览功能）
    if (typeof window.Vditor.preview !== 'function') {
        console.warn('Vditor.preview方法不可用，但继续初始化编辑器');
    }

    // 检查通过，初始化编辑器
    console.log('Vditor检查通过. 版本:', window.Vditor.version || '未知', '. 正在初始化编辑器...');
    initVditorEditor();
}

function restoreButtonState() {
    $('#editBtn, #copyBtn, #uploadBtn, #deleteBtn').show();
    $('#saveBtn, #cancelBtn').hide();
    isEditMode = false;
}

function initVditorEditor() {
    // 步骤 2: 创建 Vditor 实例
    // 使用 window.Vditor 来明确表示我们用的是全局变量。
    vditorInstance = new window.Vditor('contentArea', {
        height: '100%',
        placeholder: '请在此处输入 Markdown 内容...',
        cache: { enable: false },
        value: currentMarkdownContent,
        mode: 'wysiwyg',
        preview: {
            markdown: { sanitize: true }
        },
        upload: {
            url: commonUrl.baseUrl + 'ddgc/ContentNewController/uploadImage',
            headers: { 'Authorization': getCookie("token") || "" },
            format(files, responseText) {
                try {
                    const encryptedResponse = JSON.parse(responseText);
                    const decryptedText = sm4.decrypt(encryptedResponse.Data, '0123456789abcdeffedcba9876543210');
                    return decryptedText;
                } catch (e) {
                    console.error("处理上传响应失败:", e);
                    layer.msg('客户端处理服务器响应失败！');
                    return JSON.stringify({ "msg": "Client-side error", "code": 1, "data": {} });
                }
            },
        },
        toolbar: [
            'undo', 'redo', '|', 'bold', 'italic', 'strike', 'link', '|',
            'check', 'outdent', 'indent', '|', 'quote', 'line', 'code',
            'inline-code', 'insert-before', 'insert-after', '|', 'upload',
            'table', '|', 'export'
        ],

        // 修复后的粘贴逻辑 - 不依赖html2md
        paste: (event) => {
            console.log('--- 自定义粘贴处理器触发! ---');
            
            let html = event.clipboardData.getData('text/html');
            let text = event.clipboardData.getData('text/plain');

            // 如果没有HTML内容，直接使用纯文本
            if (!html && text) {
                if (vditorInstance) { 
                    vditorInstance.insertValue(text); 
                }
                event.preventDefault();
                return true;
            }

            // 如果有HTML内容，进行处理
            if (html) {
                // 基本的HTML清理
                html = html.replace(/\s(class|style)="[^"]*"/gi, '');      // 移除所有 class 和 style 属性
                html = html.replace(/<o:p>[\s\S]*?<\/o:p>/g, '');           // 移除 Word 特有的XML标签
                html = html.replace(/<\/?\w+:[^>]*>/g, '');                 // 移除其他XML命名空间标签
                html = html.replace(/<!--[\s\S]*?-->/g, '');                // 移除HTML注释
                html = html.replace(/<(p|span|div)\b[^>]*>\s*<\/\1>/gi, ''); // 移除空的 p, span, div 标签
                
                // 尝试使用html2md，如果不可用则使用纯文本
                let content = text || html; // 默认使用纯文本，如果没有则使用清理后的HTML
                
                if (window.Vditor && typeof window.Vditor.html2md === 'function') {
                    try {
                        content = window.Vditor.html2md(html);
                        console.log('使用html2md转换成功');
                    } catch (e) {
                        console.warn('html2md转换失败，使用纯文本:', e);
                        content = text || html;
                    }
                } else {
                    console.warn('html2md方法不可用，使用纯文本');
                    content = text || html;
                }

                if (vditorInstance) { 
                    vditorInstance.insertValue(content); 
                }
                event.preventDefault();
                return true;
            }

            // 默认行为
            return false;
        },
    });

    // 步骤 4: 更新UI
    $('#contentArea').addClass('vditor-container');
}

// 开始检查
checkAndInitVditor();

/* 
使用说明：
1. 将您原文件中第883-893行的if检查逻辑替换为上面的checkAndInitVditor()调用
2. 将原有的Vditor初始化代码替换为上面的initVditorEditor()函数
3. 这个修复版本不依赖html2md方法，即使该方法不可用也能正常工作
4. 保留了粘贴功能，会优雅地降级到纯文本处理
*/
