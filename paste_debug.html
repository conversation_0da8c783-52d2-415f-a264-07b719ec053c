<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>粘贴功能调试</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/vditor@3.11.1/dist/index.css" />
    <script src="https://cdn.jsdelivr.net/npm/vditor@3.11.1/dist/index.min.js"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        .toolbar {
            margin-bottom: 20px;
            padding: 10px;
            background: #f5f5f5;
            border-radius: 4px;
        }
        button {
            background: #007cba;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin-right: 10px;
        }
        button:hover {
            background: #005a87;
        }
        #editor {
            border: 1px solid #ddd;
            border-radius: 4px;
            min-height: 400px;
        }
        .log {
            background: #f0f0f0;
            padding: 10px;
            border-radius: 4px;
            margin-top: 20px;
            max-height: 200px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <h1>Vditor粘贴功能调试</h1>
    
    <div class="toolbar">
        <button onclick="initEditor()">初始化编辑器</button>
        <button onclick="clearLog()">清空日志</button>
        <button onclick="testPaste()">测试粘贴处理</button>
    </div>
    
    <div id="editor"></div>
    
    <div class="log" id="log">
        <div>日志输出将显示在这里...</div>
    </div>

    <script>
        let vditorInstance = null;
        
        function log(message) {
            const logDiv = document.getElementById('log');
            const time = new Date().toLocaleTimeString();
            logDiv.innerHTML += `<div>[${time}] ${message}</div>`;
            logDiv.scrollTop = logDiv.scrollHeight;
            console.log(message);
        }

        function clearLog() {
            document.getElementById('log').innerHTML = '<div>日志已清空...</div>';
        }

        // HTML清洗函数
        function cleanHtmlContent(html, fallbackText) {
            try {
                let cleaned = html
                    .replace(/\s(class|style)="[^"]*"/gi, '')
                    .replace(/<o:p[\s\S]*?<\/o:p>/gi, '')
                    .replace(/<\/?\w+:[^>]*>/g, '')
                    .replace(/<!--[\s\S]*?-->/g, '')
                    .replace(/<(p|span|div|font)\b[^>]*>\s*<\/\1>/gi, '')
                    .replace(/\s(lang|xml:lang|xmlns|face|size|color)="[^"]*"/gi, '')
                    .replace(/\s+/g, ' ')
                    .trim();

                cleaned = htmlToMarkdownManual(cleaned);
                
                if (!cleaned || cleaned.trim().length === 0) {
                    return fallbackText || '';
                }

                return cleaned;
            } catch (error) {
                log('HTML清洗过程出错: ' + error.message);
                return fallbackText || '';
            }
        }

        function htmlToMarkdownManual(html) {
            return html
                .replace(/<h1[^>]*>(.*?)<\/h1>/gi, '\n# $1\n')
                .replace(/<h2[^>]*>(.*?)<\/h2>/gi, '\n## $1\n')
                .replace(/<h3[^>]*>(.*?)<\/h3>/gi, '\n### $1\n')
                .replace(/<(strong|b)[^>]*>(.*?)<\/\1>/gi, '**$2**')
                .replace(/<(em|i)[^>]*>(.*?)<\/\1>/gi, '*$2*')
                .replace(/<p[^>]*>(.*?)<\/p>/gi, '\n$1\n')
                .replace(/<br[^>]*>/gi, '\n')
                .replace(/<li[^>]*>(.*?)<\/li>/gi, '- $1\n')
                .replace(/<\/?(ul|ol)[^>]*>/gi, '\n')
                .replace(/<a[^>]*href="([^"]*)"[^>]*>(.*?)<\/a>/gi, '[$2]($1)')
                .replace(/<code[^>]*>(.*?)<\/code>/gi, '`$1`')
                .replace(/<blockquote[^>]*>(.*?)<\/blockquote>/gi, '\n> $1\n')
                .replace(/<[^>]*>/g, '')
                .replace(/\n\s*\n\s*\n/g, '\n\n')
                .replace(/^\s+|\s+$/g, '')
                .trim();
        }

        function handlePaste(event) {
            log('=== 粘贴事件触发 ===');
            
            const clipboardData = event.clipboardData || window.clipboardData;
            if (!clipboardData) {
                log('❌ 无法访问剪贴板数据');
                return;
            }

            const html = clipboardData.getData('text/html');
            const text = clipboardData.getData('text/plain');

            log(`📋 HTML长度: ${html ? html.length : 0}`);
            log(`📋 文本长度: ${text ? text.length : 0}`);

            if (html) {
                log(`📄 原始HTML: ${html.substring(0, 100)}...`);
            }

            // 如果没有HTML内容，让Vditor处理纯文本
            if (!html || html.trim().length === 0) {
                log('ℹ️ 没有HTML内容，使用默认处理');
                return;
            }

            // 阻止默认粘贴行为
            event.preventDefault();
            event.stopPropagation();
            log('🛑 已阻止默认粘贴行为');

            // 清洗HTML内容
            const cleanedContent = cleanHtmlContent(html, text);
            log(`✨ 清洗后内容: ${cleanedContent.substring(0, 100)}...`);

            // 插入清洗后的内容
            if (vditorInstance && typeof vditorInstance.insertValue === 'function') {
                vditorInstance.insertValue(cleanedContent);
                log('✅ 内容已插入编辑器');
            } else {
                log('❌ 无法插入内容，vditorInstance不可用');
            }
        }

        function setupPasteHandler() {
            log('🔧 设置粘贴事件处理器...');
            
            setTimeout(() => {
                const contentArea = document.getElementById('editor');
                if (!contentArea) {
                    log('❌ editor元素未找到');
                    return;
                }

                // 查找Vditor的编辑区域
                const vditorElements = [
                    contentArea.querySelector('.vditor-textarea textarea'),
                    contentArea.querySelector('.vditor-wysiwyg'),
                    contentArea.querySelector('.vditor-ir'),
                    contentArea.querySelector('.vditor'),
                    contentArea
                ];

                let targetElement = null;
                for (const element of vditorElements) {
                    if (element) {
                        targetElement = element;
                        break;
                    }
                }

                if (targetElement) {
                    log(`✅ 找到目标元素: ${targetElement.className || targetElement.tagName}`);
                    
                    // 移除旧的事件监听器
                    targetElement.removeEventListener('paste', handlePaste);
                    
                    // 添加新的事件监听器
                    targetElement.addEventListener('paste', handlePaste, true);
                    
                    log('✅ 粘贴事件监听器已设置');
                } else {
                    log('❌ 未找到合适的目标元素');
                }
            }, 1000);
        }

        function initEditor() {
            log('🚀 开始初始化Vditor编辑器...');
            
            if (!window.Vditor) {
                log('❌ Vditor未加载');
                return;
            }

            // 清空编辑器容器
            document.getElementById('editor').innerHTML = '';

            try {
                vditorInstance = new Vditor('editor', {
                    height: 400,
                    mode: 'wysiwyg',
                    placeholder: '请在此输入内容，然后测试粘贴功能...',
                    value: '# 测试编辑器\n\n请复制一些富文本内容并粘贴到这里测试清洗功能。',
                    cache: { enable: false },
                    preview: {
                        markdown: { sanitize: true }
                    },
                    toolbar: [
                        'undo', 'redo', '|',
                        'bold', 'italic', 'strike', '|',
                        'line', 'quote', 'list', 'ordered-list', '|',
                        'code', 'inline-code'
                    ],
                    after: () => {
                        log('✅ Vditor初始化完成');
                        setupPasteHandler();
                    }
                });
            } catch (error) {
                log('❌ Vditor初始化失败: ' + error.message);
            }
        }

        function testPaste() {
            const testHtml = `
                <p style="color: red; font-size: 14px;">
                    <strong>这是粗体文本</strong>，
                    <em>这是斜体文本</em>
                </p>
                <h2 style="color: blue;">测试标题</h2>
                <ul>
                    <li>列表项1</li>
                    <li>列表项2</li>
                </ul>
            `;
            
            const cleaned = cleanHtmlContent(testHtml, '纯文本后备');
            log('🧪 测试清洗结果: ' + cleaned);
            
            if (vditorInstance) {
                vditorInstance.insertValue('\n\n--- 测试插入 ---\n' + cleaned);
                log('✅ 测试内容已插入');
            }
        }

        // 页面加载完成后自动初始化
        document.addEventListener('DOMContentLoaded', function() {
            log('📄 页面加载完成');
            setTimeout(initEditor, 1000);
        });
    </script>
</body>
</html>
