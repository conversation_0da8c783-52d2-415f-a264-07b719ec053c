<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Word标题识别测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-section {
            border: 1px solid #ddd;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
        }
        .result-area {
            background: #f5f5f5;
            padding: 15px;
            border-radius: 4px;
            margin: 10px 0;
            white-space: pre-wrap;
            font-family: monospace;
            max-height: 400px;
            overflow-y: auto;
        }
        button {
            background: #007cba;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #005a87;
        }
        .word-sample {
            background: #f0f8ff;
            padding: 15px;
            border-radius: 4px;
            margin: 10px 0;
            border-left: 4px solid #007cba;
            font-family: monospace;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <h1>Word标题识别和html2md测试</h1>
    
    <div class="test-section">
        <h2>🔍 Word标题HTML结构分析</h2>
        <p>以下是Word文档中常见的标题HTML结构：</p>
        
        <div class="word-sample">
            <strong>典型的Word标题结构：</strong><br><br>
            &lt;p class="MsoNormal"&gt;&lt;strong&gt;&lt;span style="font-family:宋体"&gt;1、原则和依据&lt;/span&gt;&lt;/strong&gt;&lt;/p&gt;<br>
            &lt;p class="MsoNormal"&gt;&lt;strong&gt;&lt;span style="font-family:宋体"&gt;1.1 编制依据和适用范围&lt;/span&gt;&lt;/strong&gt;&lt;/p&gt;<br><br>
            
            <strong>可能的变体：</strong><br><br>
            &lt;p&gt;&lt;strong&gt;1、标题&lt;/strong&gt;&lt;/p&gt;<br>
            &lt;p class="MsoHeading1"&gt;标题内容&lt;/p&gt;<br>
            &lt;p&gt;&lt;span style="font-weight:bold"&gt;1.1 标题&lt;/span&gt;&lt;/p&gt;
        </div>
        
        <button onclick="testTitleRecognition()">🔍 测试标题识别</button>
        <button onclick="testHtml2md()">🧪 测试html2md方法</button>
    </div>
    
    <div class="test-section">
        <h2>📊 测试结果</h2>
        <div id="testResult" class="result-area">点击测试按钮查看结果...</div>
    </div>
    
    <div class="test-section">
        <h2>🎯 期望的转换效果</h2>
        <ul>
            <li><code>1、原则和依据</code> → <code># 1、原则和依据</code></li>
            <li><code>1.1 编制依据和适用范围</code> → <code>## 1.1 编制依据和适用范围</code></li>
            <li><strong>不应该</strong>变成斜体：<code>*标题*</code></li>
        </ul>
    </div>

    <script>
        function testTitleRecognition() {
            // 模拟真实Word文档的标题HTML
            const wordTitleHtml = `
                <p class="MsoNormal"><strong><span style="font-family:宋体">1、原则和依据</span></strong></p>
                <p class="MsoNormal"><strong><span style="font-family:宋体">1.1 编制依据和适用范围</span></strong></p>
                <p class="MsoNormal"><span style="font-family:宋体">《2024年安徽电网稳定运行规定》（以下简称"本规定"）针对2024年中安徽电网接线方式。</span></p>
                <p><strong>2、运行要求</strong></p>
                <p class="MsoHeading1">3、应急处置</p>
            `;
            
            console.log('测试Word标题识别...');
            
            let result = wordTitleHtml;
            
            // 应用标题识别规则
            console.log('原始HTML:', result);
            
            // 识别Word中常见的标题模式
            result = result
                // 识别数字开头的标题模式（如：1、标题 或 1.1 标题）
                .replace(/<p[^>]*>\s*<strong[^>]*>\s*<span[^>]*>\s*(\d+[、.][\d.]*)\s*([^<]+)\s*<\/span>\s*<\/strong>\s*<\/p>/gi, '<h2>$1 $2</h2>')
                .replace(/<p[^>]*>\s*<strong[^>]*>\s*(\d+[、.][\d.]*)\s*([^<]+)\s*<\/strong>\s*<\/p>/gi, '<h2>$1 $2</h2>')
                // 识别纯数字标题
                .replace(/<p[^>]*>\s*<strong[^>]*>\s*(\d+)\s*([^<]+)\s*<\/strong>\s*<\/p>/gi, '<h1>$1 $2</h1>')
                // 识别带有MsoHeading样式的标题
                .replace(/<p[^>]*class="[^"]*MsoHeading[^"]*"[^>]*>(.*?)<\/p>/gi, '<h2>$1</h2>');
            
            console.log('标题识别后:', result);
            
            // 清洗HTML
            result = result
                .replace(/\sstyle="[^"]*"/gi, '')
                .replace(/\sclass="[^"]*"/gi, '')
                .replace(/\s+/g, ' ')
                .trim();
            
            console.log('清洗后:', result);
            
            // 转换为Markdown
            const markdown = htmlToMarkdownManual(result);
            
            document.getElementById('testResult').innerHTML = 
                '=== Word标题识别测试 ===\n\n' +
                '原始HTML:\n' + wordTitleHtml + '\n\n' +
                '标题识别后:\n' + result + '\n\n' +
                '最终Markdown:\n' + markdown;
        }

        function testHtml2md() {
            console.log('测试Vditor的html2md方法...');
            
            const testHtml = '<h1>标题1</h1><p><strong>粗体</strong>和<em>斜体</em></p>';
            
            let result = '=== html2md方法测试 ===\n\n';
            result += 'Vditor对象存在: ' + (!!window.Vditor) + '\n';
            
            if (window.Vditor) {
                result += 'Vditor版本: ' + (window.Vditor.version || '未知') + '\n';
                result += 'html2md方法类型: ' + typeof window.Vditor.html2md + '\n';
                result += 'html2md方法存在: ' + (typeof window.Vditor.html2md === 'function') + '\n\n';
                
                if (typeof window.Vditor.html2md === 'function') {
                    try {
                        const markdown = window.Vditor.html2md(testHtml);
                        result += '测试HTML: ' + testHtml + '\n';
                        result += 'html2md结果: ' + markdown + '\n';
                        result += '转换成功: ✅\n';
                    } catch (error) {
                        result += '转换失败: ❌\n';
                        result += '错误信息: ' + error.message + '\n';
                    }
                } else {
                    result += 'html2md方法不可用 ❌\n';
                    result += '可能的原因:\n';
                    result += '1. Vditor版本不支持html2md\n';
                    result += '2. CDN加载的版本不完整\n';
                    result += '3. 需要额外的插件或配置\n';
                }
            } else {
                result += 'Vditor对象不存在 ❌\n';
            }
            
            document.getElementById('testResult').innerHTML = result;
        }

        function htmlToMarkdownManual(html) {
            let result = html;
            
            // 标题转换
            result = result
                .replace(/<h1[^>]*>(.*?)<\/h1>/gi, '\n# $1\n')
                .replace(/<h2[^>]*>(.*?)<\/h2>/gi, '\n## $1\n')
                .replace(/<h3[^>]*>(.*?)<\/h3>/gi, '\n### $1\n')
                .replace(/<h4[^>]*>(.*?)<\/h4>/gi, '\n#### $1\n')
                .replace(/<h5[^>]*>(.*?)<\/h5>/gi, '\n##### $1\n')
                .replace(/<h6[^>]*>(.*?)<\/h6>/gi, '\n###### $1\n');
            
            // 文本格式转换
            result = result
                .replace(/<(strong|b)[^>]*>(.*?)<\/\1>/gi, '**$2**')
                .replace(/<(em|i)[^>]*>(.*?)<\/\1>/gi, '*$2*');
            
            // 段落和其他元素
            result = result
                .replace(/<p[^>]*>(.*?)<\/p>/gi, '\n$1\n')
                .replace(/<br[^>]*>/gi, '\n')
                .replace(/<[^>]*>/g, '')
                .replace(/\n\s*\n\s*\n/g, '\n\n')
                .replace(/^\s+|\s+$/g, '')
                .trim();
            
            return result;
        }

        // 页面加载时检查Vditor状态
        document.addEventListener('DOMContentLoaded', function() {
            setTimeout(() => {
                console.log('页面加载完成，检查Vditor状态...');
                console.log('Vditor存在:', !!window.Vditor);
                if (window.Vditor) {
                    console.log('Vditor版本:', window.Vditor.version);
                    console.log('html2md方法:', typeof window.Vditor.html2md);
                }
            }, 2000);
        });
    </script>
</body>
</html>
