<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>左右布局编辑器 (Vditor整合版)</title>
    <!-- 保留所有必要的库 -->
    <link rel="stylesheet" href="../../../lib/ol3/ol.css">
    <link rel="stylesheet" href="../../../lib/layui/css/layui.css">
    <link rel="stylesheet" href="../../css/ddgc.css">
    <script src="../../../lib/jquery/jquery-2.1.4.min.js"></script>
    <script src="../../../lib/ol3/ol.js"></script>
    <script src="../../../lib/layui/layui.all.js"></script>
    <!-- 引入 Vditor 的 CSS 和 JS - 使用多个CDN源确保加载成功 -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/vditor@3.11.1/dist/index.css" />
    <!-- 引入 Turndown.js 用于HTML转Markdown -->
    <script src="https://cdn.jsdelivr.net/npm/turndown/dist/turndown.js"></script>
    <script>
        // Vditor加载检查和备用CDN
        function loadVditorWithFallback() {
            return new Promise((resolve, reject) => {
                // 主CDN源
                const primaryScript = document.createElement('script');
                primaryScript.src = 'https://cdn.jsdelivr.net/npm/vditor@3.11.1/dist/index.min.js';
                primaryScript.onload = () => {
                    console.log('Vditor loaded from primary CDN');
                    resolve();
                };
                primaryScript.onerror = () => {
                    console.warn('Primary CDN failed, trying backup...');
                    // 备用CDN源
                    const backupScript = document.createElement('script');
                    backupScript.src = 'https://unpkg.com/vditor@3.11.1/dist/index.min.js';
                    backupScript.onload = () => {
                        console.log('Vditor loaded from backup CDN');
                        resolve();
                    };
                    backupScript.onerror = () => {
                        console.error('All CDN sources failed');
                        reject(new Error('Failed to load Vditor from all CDN sources'));
                    };
                    document.head.appendChild(backupScript);
                };
                document.head.appendChild(primaryScript);
            });
        }

        // 页面加载完成后加载Vditor
        document.addEventListener('DOMContentLoaded', function() {
            loadVditorWithFallback().catch(error => {
                console.error('Vditor loading failed:', error);
                // 显示友好的错误提示
                if (typeof layer !== 'undefined') {
                    layer.msg('编辑器组件加载失败，请检查网络连接或刷新页面重试', {time: 5000});
                }
            });
        });
    </script>
    <script src="../../../config/config.js"></script>

    <script src="../../../js/common/common.js"></script>
    <script language="JavaScript" type="text/javascript" src="../../../lib/doSM3/sm3.js"></script>
    <script language="JavaScript" type="text/javascript" src="../../../lib/doSM3/sm4.js"></script>
    <script language="JavaScript" type="text/javascript" src="../../../lib/doSM3/smutils.js"></script>
    <script language="JavaScript" type="text/javascript" src="../../../lib/doSM3/byte.js"></script>
    <script src="../../../lib/jwt/jwt.js"></script>
    <script src="../../../lib/md5/md5.js"></script>
    <script src="../../../ddgc/js/changliang.js"></script>


    <script src="../../../ddgc/js/axios.min.js"></script>

    <style>
        /* 基础样式重置 */
        body {
            margin: 0;
            padding: 0;
            display: flex;
            height: 100vh;
            width: 100vw;
            overflow: hidden;
            color: #333;
            line-height: 1.5;
            font-family: "Microsoft YaHei", "Segoe UI", Arial, sans-serif !important;
        }

        /* 左侧菜单容器 */
        .left-menu {
            width: 250px;
            min-width: 250px;
            max-width: 250px;
            background: #f8f9fa;
            border-right: 1px solid #dee2e6;
            overflow-y: auto;
            position: fixed;
            height: 100vh;
            top: 0;
            left: 0;
            z-index: 10;
            box-sizing: border-box;
            display: flex;
            flex-direction: column;
            font-family: "Microsoft YaHei", "Segoe UI", Arial, sans-serif !important;
        }

        /* 菜单头部样式 */
        .ddgcMainLeftHeadDiv {
            background: #fff !important;
            border-bottom: 1px solid #eee;
            /*padding: 12px 15px;*/
            position: sticky;
            top: 0;
            z-index: 11;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
        }

        .ddgcMainLeftHeadDiv .icon {
            display: inline-block;
            width: 4px;
            vertical-align: middle;
            background: #0F82C5 !important;
            margin-right: 10px;
            border-radius: 2px;
        }

        #secondInputText {
            display: inline-block;
            vertical-align: middle;
            font-size: 15px;
            font-weight: 600;
            color: #333 !important;
        }

        /* 版本选择器 */
        .version-selector {
            padding: 12px 15px;
            background: #fff;
            border-bottom: 1px solid #eee;
            position: sticky;
            top: 44px;
            z-index: 10;
        }

        .version-selector select {
            width: 100%;
            padding: 8px 12px;
            border: 1px solid #ced4da;
            border-radius: 4px;
            font-size: 13px;
            background-color: #fff;
            transition: border-color 0.15s ease-in-out;
        }

        .version-selector select:focus {
            border-color: #80bdff;
            outline: 0;
            box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
        }

        /* 章节列表容器 */
        #chapter-list {
            flex: 1;
            overflow-y: auto;
            padding: 5px 0;
        }

        /* 菜单项基础样式 */
        .menu-item, .sub-item {
            position: relative;
            padding: 8px 15px 8px 30px;
            cursor: pointer;
            transition: all 0.2s ease;
            border-left: 3px solid transparent;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            font-size: 14px;
            font-family: "Microsoft YaHei", "Segoe UI", Arial, sans-serif !important;
        }

        /* 一级菜单项 */
        .menu-item {
            background-color: #f8f9fa;
            border-bottom: 1px solid #e9ecef;
            padding-left: 30px;
        }

        /* 子菜单项 */
        .sub-item {
            position: relative;
            padding: 8px 15px 8px 30px;
            cursor: pointer;
            transition: all 0.2s ease;
            border-left: 3px solid transparent;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            font-size: 14px;
            background-color: #ffffff;
        }

        /* 二级子菜单项 */
        .sub-item .sub-item {
            padding-left: 45px;
        }

        /* 三级子菜单项 */
        .sub-item .sub-item .sub-item {
            padding-left: 60px;
        }

        /* 菜单项悬停状态 */
        .menu-item:hover, .sub-item:hover {
            background-color: #e9ecef;
            color: #0F82C5;
        }

        /* 活动/选中状态 */
        .menu-item.active, .sub-item.active {
            background-color: #e6f0fa !important;
            color: #0F82C5 !important;
            font-weight: 600;
            border-left: 3px solid #0F82C5 !important;
        }

        /* 菜单项/子菜单项选中状态下的章节名称 */
        .menu-item.active .chapter,
        .sub-item.active .chapter {
            font-weight: 600;
            color: #0F82C5;
        }

        /* 章节名称 */
        .chapter {

            vertical-align: middle;
            max-width: calc(100% - 30px);
            overflow: hidden;
            text-overflow: ellipsis;
            font-weight: normal;
            font-family: "Microsoft YaHei", "Segoe UI", Arial, sans-serif !important;
        }

        /* 切换图标 */
        .toggle-icon {
            display: inline-block;
            width: 20px;
            height: 20px;
            margin-right: 12px;
            vertical-align: middle;
            text-align: center;
            color: #6c757d;
            transition: transform 0.2s ease;
            position: absolute;
            left: 10px;
            top: 50%;
            transform: translateY(-50%);
        }

        .toggle-icon::before {
            content: "▶";
            font-family: 'Font Awesome 5 Free';
            font-weight: 900;
            font-size: 12px;
        }

        /* 展开状态的切换图标 */
        .menu-item.expanded > .toggle-icon::before,
        .sub-item.expanded > .toggle-icon::before {
            content: "▼";
        }

        /* 子菜单容器 */
        .sub-menu {
            max-height: 0;
            overflow: hidden;
            transition: max-height 0.3s ease-out;
            background-color: #f8f9fa;
            padding-left: 10px;
            border-left: 2px solid #e9ecef;
            margin-left: 5px;
        }

        /* 展开状态的子菜单 */
        .menu-item.expanded + .sub-menu,
        .sub-item.expanded + .sub-menu {
            max-height: none;
        }

        /* 增加子菜单项的左侧边框，增强层次感 */
        .sub-menu .sub-item {
            border-left: none;
            margin-left: 0;
            padding-left: 30px;
            position: relative;
        }

        .sub-menu .sub-menu .sub-item {
            background-color: #f9f9f9;
        }

        /* 右侧编辑器区域 */
        .right-editor {
            flex: 1;
            display: flex;
            flex-direction: column;
            margin-left: 250px;
            height: 100vh;
            overflow-y: auto;
            background-color: #f8f9fa;
            font-family: Arial, sans-serif !important;
        }

        .editor-content {
            flex: 1;
            padding: 20px;
            box-sizing: border-box;
        }

        /* Word文档容器样式 */
        .word-document-container {
            background-color: #fff;
            padding: 20px;
            border-radius: 4px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            min-height: 500px;
            font-family: "Times New Roman", SimSun, serif !important;
        }

        #contentArea {
            font-family: "Times New Roman", SimSun, serif !important;
            line-height: 1.5;
            color: #000;
            margin-top: 9px;
        }

        /* 按钮样式 */
        .layui-btn {
            display: inline-block;
            padding: 6px 12px;
            margin-bottom: 0;
            font-size: 14px;
            font-weight: 400;
            line-height: 1.5;
            text-align: center;
            white-space: nowrap;
            vertical-align: middle;
            cursor: pointer;
            background-image: none;
            border: 1px solid transparent;
            border-radius: 4px;
            color: #fff;
            background-color: #0F82C5;
            border-color: #0d6ea3;
            transition: all 0.2s ease;
        }

        .layui-btn:hover {
            background-color: #0d6ea3;
            border-color: #0b5d8a;
        }

        .layui-btn-normal {
            background-color: #5cb85c;
            border-color: #4cae4c;
        }

        .layui-btn-normal:hover {
            background-color: #4cae4c;
            border-color: #398439;
        }

        /* 加载指示器 */
        #loading-indicator {
            text-align: center;
            padding: 40px;
            border: 1px solid #ddd;
            background: #fff;
            border-radius: 4px;
            margin-bottom: 20px;
        }

        .layui-icon-loading {
            color: #0F82C5;
            font-size: 30px;
            margin-bottom: 10px;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .left-menu {
                width: 220px;
                min-width: 220px;
                max-width: 220px;
            }

            .right-editor {
                margin-left: 220px;
            }

            .menu-item, .sub-item {
                padding-left: 25px;
                font-size: 13px;
            }

            .sub-item {
                padding-left: 40px;
            }

            .sub-item .sub-item {
                padding-left: 55px;
            }
        }

        @media (max-width: 576px) {
            .left-menu {
                position: absolute;
                z-index: 1000;
                transform: translateX(-100%);
                transition: transform 0.3s ease;
            }

            .left-menu.active {
                transform: translateX(0);
            }

            .right-editor {
                margin-left: 0;
            }

            /* 添加菜单切换按钮样式 */
            .menu-toggle {
                display: block;
                position: fixed;
                top: 10px;
                left: 10px;
                z-index: 1001;
                background: #0F82C5;
                color: white;
                border: none;
                padding: 5px 10px;
                border-radius: 4px;
                cursor: pointer;
            }
        }


        /* 添加复选框样式 */
        .chapter-checkbox {
            display: inline-block;
            width: 16px;
            height: 16px;
            margin-right: 8px;
            vertical-align: middle;
            position: relative;
            top: -1px;
            cursor: pointer;
        }

        /* 复选框容器 */
        .checkbox-container {
            display: inline-block;
            vertical-align: middle;
            margin-right: 5px;
        }

        .catalogue-title {
            font-size: 14px;
            font-weight: bold;
            margin-bottom: 5px;
            /*  padding-bottom: 10px;*/
            padding-left: 1.5%;
            border-bottom: 0px solid #ddd;
            position: absolute;
            width: 77%;
        }

        .xiayi {
            padding-top: 25px;
        }

        /* 搜索高亮样式 */
        .search-highlight {
            background-color: #fffacd !important;
        }

        .highlight-text {
            background-color: yellow;
            font-weight: bold;
            color: #000;
        }

        .highlighted {
            background-color: yellow;
            font-weight: bold;
            color: #000;
        }

        .juzhong {
            padding-top: 0px !important;
            padding-bottom: 0px !important;
        }

        .anniu {
            height: 2.8vh;
            line-height: 1vh;
            margin-left: 0.4vw;
        }

        /* Vditor 特定样式 */
        #contentArea.vditor-container {
            padding: 0;
            box-shadow: none;
        }

        .vditor {
            flex: 1;
            border-radius: 4px;
            border: 1px solid #dcdfe6;
        }

        .preview-area {
            flex: 1;
            background-color: #fff;
            padding: 20px;
            border-radius: 4px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            overflow-y: auto;
            border: 1px solid #dcdfe6;
        }

        .vditor-search-mark {
            background-color: yellow;
            color: black;
        }

        .vditor-toolbar {
            margin: auto;
            border-bottom: 0;
        }
    </style>
</head>
<body>
<div class="left-menu">
    <div class="ddgcMainLeftHeadDiv">
        <span class="icon"></span>
        <span id="secondInputText">文档章节</span>
    </div>
    <div style="padding: 10px; background: #fff; border-bottom: 1px solid #eee;">
        <button id="batchDeleteBtn" class="layui-btn layui-btn-danger" style="width: 100%; display: none;">
            <i class="layui-icon layui-icon-delete"></i> 批量删除选中章节
        </button>
    </div>
    <div class="version-selector">
        <select id="version-select">
            <option value="">加载版本中...</option>
        </select>
    </div>
    <div id="chapter-list"></div>
</div>

<div class="right-editor">
    <div class="catalogue-title-wrapper" style="display: none;">
        <div class="catalogue-title" style="display: flex; justify-content: space-between; align-items: center;">
            <div>
                <button id="editBtn" class="layui-btn layui-btn-sm anniu">编辑</button>
                <button id="saveBtn" class="layui-btn layui-btn-warm layui-btn-sm anniu" style="display: none;">保存
                </button>
                <button id="cancelBtn" class="layui-btn layui-btn-primary layui-btn-sm anniu" style="display: none;">
                    取消
                </button>
                <button id="copyBtn" class="layui-btn layui-btn-sm anniu">复制</button>
                <button id="uploadBtn" class="layui-btn layui-btn-normal layui-btn-sm anniu" style="margin-left: 10px;">
                    上传新文件
                </button>
                <button id="deleteBtn" class="layui-btn layui-btn-danger delete-btn layui-btn-sm anniu"
                        style="margin-left: 10px;">删除
                </button>
            </div>
            <div style="display: flex; align-items: center;margin-left: auto;">
                <input type="text" id="searchInput" placeholder="请输入查询内容" class="layui-input"
                       style="width: 200px; margin-right: 5px;height: 25px;font-size: 14px;">
                <button id="searchButton" class="layui-btn layui-btn-sm anniu">搜索</button>
                <button id="exportBtn" class="layui-btn layui-btn-sm anniu">导出</button>
                <button id="resetButton" class="layui-btn layui-btn-sm anniu" style="margin-right: 6px;">重置</button>
            </div>
        </div>
    </div>
    <div class="editor-content" id="editor-content">
        <div id="contentArea">
            <div id="default-prompt" style="text-align: center; margin-top: 20px; color: #888;">
                <h3>请从左侧选择章节查看内容</h3>
            </div>
        </div>
    </div>
</div>

<input type="file" id="hiddenFileInput" accept=".md,.txt" style="display: none;">

<script type="application/javascript">
    layui.use(['form', 'layer'], function () {
        var form = layui.form,
            layer = layui.layer,
            $ = layui.$;

        var versionId = 0, currentChapterId = null, currentVersionId = null;
        var currentMarkdownContent = '';
        var vditorInstance = null, isEditMode = false;
        var currentSearchKeyword = null;

        // --- 初始化和目录加载 ---
        loadVersionList();

        $('#version-select').on('change', function () {
            versionId = $(this).val();
            if (versionId) {
                loadChapterList(versionId);
                $('#contentArea').html('<div id="default-prompt" style="text-align: center; margin-top: 20px; color: #888;"><h3>请选择章节</h3></div>').removeClass('vditor-container');
                $('.catalogue-title-wrapper').hide();
            }
        });

        function loadVersionList() {
            $ajax({type: "get", url: 'ddgc/VersionController/getListAll'}, function (res) {
                if (res.code === 200 && res.data && res.data.length > 0) {
                    let options = '<option value="0">请选择版本</option>';
                    res.data.forEach(v => {
                        options += `<option value="${v.versionId}">${v.versionName}</option>`;
                    });
                    $('#version-select').html(options);
                    form.render('select');
                    const firstId = res.data[0].versionId;
                    $('#version-select').val(firstId);
                    versionId = firstId;
                    loadChapterList(firstId);
                } else {
                    $('#version-select').html('<option value="">无可用版本</option>');
                    form.render('select');
                    layer.msg(res.msg || '无可用版本');
                }
            });
        }

        function loadChapterList(vid) {
            $ajax({type: "get", url: 'ddgc/ChapterController/getChapterTree', params: {versionId: vid}}, res => {
                if (res.code === 200 && res.data && res.data.chapterEntityList) {
                    catalogDisplay(res.data.chapterEntityList);
                } else {
                    $('#chapter-list').empty();
                    layer.msg(res.msg || '获取章节失败');
                }
            });
        }

        function catalogDisplay(chapters, level = 0) {
            const expandedState = JSON.parse(localStorage.getItem('expandedChapters') || '{}');
            let str = '';
            chapters.forEach(c => {
                const hasChildren = c.chapterEntityList && c.chapterEntityList.length > 0;
                const itemClass = (level === 0) ? 'menu-item' : 'sub-item';
                const isExpanded = expandedState[c.id.id] ? 'expanded' : '';
                let paddingLeft = 30 + (level * 15);
                str += `
            <div class="${itemClass} ${isExpanded}" data-target="${c.id.id}" data-value="${c.id.versionId}" title="${c.chapterName}" style="padding-left: ${paddingLeft}px;">
                <input type="checkbox" class="chapter-checkbox" data-id="${c.id.id}" data-version="${c.id.versionId}">
                ${hasChildren ? '<span class="toggle-icon"></span>' : ''}
                <span class="chapter">${c.chapterName}</span>
            </div>
            ${hasChildren ? `<div class="sub-menu">${catalogDisplay(c.chapterEntityList, level + 1)}</div>` : ''}`;
            });
            if (level === 0) {
                $('#chapter-list').html(str);
                initCheckboxEvents();
                Object.keys(expandedState).forEach(id => {
                    if (expandedState[id]) {
                        $(`[data-target="${id}"]`).addClass('expanded').next('.sub-menu').css('max-height', 'none');
                    }
                });
            }
            return str;
        }

        // 目录交互
        $(document).on('click', '.menu-item, .sub-item', function (e) {
            const $target = $(e.target);
            // 阻止事件冒泡到父级，避免不必要的行为
            if ($target.is('.toggle-icon') || $target.is('input.chapter-checkbox')) {
                e.stopPropagation();
                // toggle-icon的逻辑单独处理
                if ($target.is('.toggle-icon')) {
                    toggleSubMenu($(this));
                }
                // checkbox的逻辑由其自身的change事件处理
                return;
            }

            $('.menu-item, .sub-item').removeClass('active');
            $(this).addClass('active');

            // 在调用 getHtml 时，检查当前是否处于搜索模式
            getHtml($(this).attr('data-target'), $(this).attr('data-value'), currentSearchKeyword);
        });

        function toggleSubMenu($parent) {
            $parent.toggleClass('expanded');
            const $subMenu = $parent.next('.sub-menu');
            $subMenu.css('max-height', $parent.hasClass('expanded') ? '9999px' : '0');
            const expandedState = JSON.parse(localStorage.getItem('expandedChapters') || '{}');
            expandedState[$parent.attr('data-target')] = $parent.hasClass('expanded');
            localStorage.setItem('expandedChapters', JSON.stringify(expandedState));
        }

        // getHtml
        function getHtml(chapterId, versionId, keywordToHighlight = null) {
            if (isEditMode) {
                layer.confirm('正在编辑，切换将丢失未保存的更改，确定吗？', {title: '提示'}, index => {
                    layer.close(index);
                    exitEditMode(false);
                    fetchAndRenderContent(chapterId, versionId, keywordToHighlight);
                });
            } else {
                fetchAndRenderContent(chapterId, versionId, keywordToHighlight);
            }
        }


        //fetchAndRenderContent
        function fetchAndRenderContent(chapterId, versionId, keywordToHighlight = null) {
            currentChapterId = chapterId;
            currentVersionId = versionId;
            const $currentItem = $(`[data-target="${chapterId}"]`);

            if ($currentItem.next('.sub-menu').length > 0 && $currentItem.next('.sub-menu').children().length > 0) {
                $('.catalogue-title-wrapper').hide();
                $('#contentArea').html(`<div id="default-prompt" style="text-align: center; margin-top: 20px; color: #888;"><h3>请选择章节查看内容。</h3></div>`).removeClass('vditor-container');
                return;
            }

            $('.catalogue-title-wrapper').show();

            $('#contentArea').html(
                '<div style="text-align: center; padding: 100px 0;">' +
                '    <i class="layui-icon layui-icon-loading layui-anim layui-anim-rotate layui-anim-loop" style="font-size: 30px; color: #0F82C5;"></i>' +
                '    <p style="margin-top: 10px; color: #999;">内容加载中...</p>' +
                '</div>'
            ).removeClass('vditor-container');


            $ajax({
                type: 'GET',
                url: `ddgc/ContentNewController/${chapterId}/${versionId}`,
            }, function (res) {
                if (res.code === 200) {
                    var contentData = res.data;
                    currentMarkdownContent = (contentData && contentData.contentText) ? contentData.contentText : '';
                } else {
                    // layer.msg(res.msg || '获取内容失败');
                    currentMarkdownContent = '';
                }
                renderPreviewMode(keywordToHighlight);
            }, function (xhr) {
                currentMarkdownContent = '';
                layer.msg('请求内容失败，请稍后重试');
                renderPreviewMode(keywordToHighlight);
            });
        }


        // 修改 renderPreviewMode
        function renderPreviewMode(keywordToHighlight = null) {
            $('#contentArea').html('').removeClass('vditor-container');
            const previewContainer = $('<div class="preview-area"></div>').appendTo('#contentArea');

            if (currentMarkdownContent) {
                Vditor.preview(previewContainer[0], currentMarkdownContent, {
                    hljs: {style: 'github'},
                    markdown: {
                        sanitize: true, // 保持安全净化
                    },
                    after() {
                        // 渲染完成后，如果有关建词，则执行高亮函数
                        if (keywordToHighlight) {
                            jquerySearchHighlight(keywordToHighlight);
                        }
                    }
                });
            } else {
                previewContainer.html('<p style="text-align:center;padding:20px;height: 81vh;line-height: 81vh;font-weight: 900">当前章节无内容。</p>');
            }
        }


        /**
         * 手动遍历DOM进行高亮
         * @param {string} keyword 要高亮的关键词
         */
        function jquerySearchHighlight(keyword) {
            if (!keyword) return;

            const previewArea = document.querySelector('.preview-area');
            if (!previewArea) {
                console.warn("jquerySearchHighlight: previewArea not found.");
                return;
            }

            // 创建一个安全的正则表达式，转义特殊字符
            const regex = new RegExp(keyword.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'), 'gi');

            // 创建一个高亮用的 span 元素模板
            const highlightSpan = document.createElement('span');
            highlightSpan.className = 'vditor-search-mark';

            let matchFound = false;

            // 递归函数，用于遍历所有DOM节点
            function walkAndHighlight(node) {
                // 1. 如果是文本节点，执行高亮
                if (node.nodeType === 3) {
                    const text = node.nodeValue;
                    if (text.match(regex)) {
                        matchFound = true;
                        const fragment = document.createDocumentFragment();
                        let lastIndex = 0;

                        text.replace(regex, (match, offset) => {
                            // 添加匹配前的文本
                            const beforeText = text.slice(lastIndex, offset);
                            if (beforeText) {
                                fragment.appendChild(document.createTextNode(beforeText));
                            }

                            // 添加高亮后的文本
                            const highlighted = highlightSpan.cloneNode(false);
                            highlighted.appendChild(document.createTextNode(match));
                            fragment.appendChild(highlighted);

                            lastIndex = offset + match.length;
                        });

                        // 添加剩余的文本
                        const afterText = text.slice(lastIndex);
                        if (afterText) {
                            fragment.appendChild(document.createTextNode(afterText));
                        }

                        // 用我们创建的包含高亮标签的文档片段替换原始文本节点
                        node.parentNode.replaceChild(fragment, node);
                    }
                }
                // 2. 如果是元素节点，则递归其子节点
                else if (node.nodeType === 1 && node.nodeName !== 'SCRIPT' && node.nodeName !== 'STYLE' && !node.classList.contains('vditor-search-mark')) {
                    // 从后向前遍历子节点
                    for (let i = node.childNodes.length - 1; i >= 0; i--) {
                        walkAndHighlight(node.childNodes[i]);
                    }
                }
            }

            // 从预览区域的根节点开始遍历
            walkAndHighlight(previewArea);

            // 如果找到了匹配项，自动滚动到第一个高亮的位置
            if (matchFound) {
                const firstHighlight = previewArea.querySelector('.vditor-search-mark');
                if (firstHighlight) {
                    firstHighlight.scrollIntoView({behavior: 'smooth', block: 'center'});
                }
            }
        }

        // 增强的HTML清洗函数 - 专门处理Word文档
        function cleanHtmlContent(html, fallbackText) {
            try {
                console.log('开始清洗HTML，原始长度:', html.length);

                // 第一步：预处理 - 移除Word文档的头部和尾部
                let cleaned = html;

                // 提取body内容，如果存在的话
                const bodyMatch = cleaned.match(/<body[^>]*>([\s\S]*)<\/body>/i);
                if (bodyMatch) {
                    cleaned = bodyMatch[1];
                    console.log('提取body内容，长度:', cleaned.length);
                }

                // 第二步：预处理Word标题（在清洗前识别标题模式）
                console.log('识别Word标题模式...');
                console.log('Body内容前500字符:', cleaned.substring(0, 500));

                // 先保存原始内容用于对比
                const beforeTitleProcessing = cleaned;

                // 识别Word中常见的标题模式并转换为标准HTML标题
                cleaned = cleaned
                    // 专门针对您的Word格式：<p><b><span>数字 <font>标题</font></span></b></p>
                    .replace(/<p[^>]*>\s*<b[^>]*>\s*<span[^>]*>\s*(\d+[、.][\d.]*)\s*<font[^>]*>([^<]+)<\/font>\s*<\/span>\s*<\/b>\s*<\/p>/gi, '<h2>$1 $2</h2>')
                    // 变体：没有font标签的情况
                    .replace(/<p[^>]*>\s*<b[^>]*>\s*<span[^>]*>\s*(\d+[、.][\d.]*)\s*([^<]+)\s*<\/span>\s*<\/b>\s*<\/p>/gi, '<h2>$1 $2</h2>')
                    // 原有的strong标签格式
                    .replace(/<p[^>]*>\s*<strong[^>]*>\s*<span[^>]*>\s*(\d+[、.][\d.]*)\s*([^<]+)\s*<\/span>\s*<\/strong>\s*<\/p>/gi, '<h2>$1 $2</h2>')
                    .replace(/<p[^>]*>\s*<strong[^>]*>\s*(\d+[、.][\d.]*)\s*([^<]+)\s*<\/strong>\s*<\/p>/gi, '<h2>$1 $2</h2>')
                    // 识别纯数字标题
                    .replace(/<p[^>]*>\s*<strong[^>]*>\s*(\d+)\s*([^<]+)\s*<\/strong>\s*<\/p>/gi, '<h1>$1 $2</h1>')
                    .replace(/<p[^>]*>\s*<b[^>]*>\s*<span[^>]*>\s*(\d+)\s*<font[^>]*>([^<]+)<\/font>\s*<\/span>\s*<\/b>\s*<\/p>/gi, '<h1>$1 $2</h1>')
                    // 识别带有MsoHeading样式的标题
                    .replace(/<p[^>]*class="[^"]*MsoHeading[^"]*"[^>]*>(.*?)<\/p>/gi, '<h2>$1</h2>')
                    // 更宽松的匹配 - 任何包含数字和点号的粗体文本
                    .replace(/<p[^>]*>\s*<b[^>]*>[\s\S]*?(\d+[、.][\d.]*[\s\S]*?)<\/b>\s*<\/p>/gi, '<h2>$1</h2>')
                    .replace(/<p[^>]*>\s*<strong[^>]*>[\s\S]*?(\d+[、.][\d.]*[\s\S]*?)<\/strong>\s*<\/p>/gi, '<h2>$1</h2>');

                // 检查是否有标题被识别
                const titleMatches = cleaned.match(/<h[1-6][^>]*>/gi);
                console.log('识别到的标题数量:', titleMatches ? titleMatches.length : 0);
                if (titleMatches) {
                    console.log('识别到的标题:', titleMatches);
                }

                // 如果没有识别到标题，显示更多调试信息
                if (!titleMatches || titleMatches.length === 0) {
                    console.log('⚠️ 未识别到标题，显示更多调试信息:');
                    // 查找所有的strong标签
                    const strongMatches = beforeTitleProcessing.match(/<strong[^>]*>[\s\S]*?<\/strong>/gi);
                    if (strongMatches) {
                        console.log('找到的strong标签:', strongMatches.slice(0, 3)); // 只显示前3个
                    }
                    // 查找所有的p标签
                    const pMatches = beforeTitleProcessing.match(/<p[^>]*>[\s\S]*?<\/p>/gi);
                    if (pMatches) {
                        console.log('找到的p标签前3个:', pMatches.slice(0, 3));
                    }
                }

                console.log('标题识别完成，开始深度清洗...');

                // 第三步：深度清洗Word特有的标签和属性
                cleaned = cleaned
                    // 移除Word的XML命名空间标签
                    .replace(/<\/?\w+:[^>]*>/g, '')
                    // 移除Word特有的标签
                    .replace(/<o:p[\s\S]*?<\/o:p>/gi, '')
                    .replace(/<!\[if[\s\S]*?\]>/gi, '')
                    .replace(/<!\[endif\]>/gi, '')
                    // 移除所有style属性（Word生成大量内联样式）
                    .replace(/\sstyle="[^"]*"/gi, '')
                    // 移除所有class属性
                    .replace(/\sclass="[^"]*"/gi, '')
                    // 移除其他Word属性
                    .replace(/\s(lang|xml:lang|xmlns|face|size|color|width|height|align|valign)="[^"]*"/gi, '')
                    // 移除HTML注释和Word片段标记
                    .replace(/<!--[\s\S]*?-->/g, '')
                    .replace(/<!--StartFragment-->/g, '')
                    .replace(/<!--EndFragment-->/g, '')
                    // 清理残留的属性值和引号
                    .replace(/[^>]*;[^>]*>/g, '>')
                    .replace(/"\s*>/g, '>')
                    // 移除空的标签
                    .replace(/<(p|span|div|font|strong|b|em|i)\b[^>]*>\s*<\/\1>/gi, '')
                    // 清理多余的空白字符
                    .replace(/\s+/g, ' ')
                    .trim();

                console.log('深度清洗后的HTML前200字符:', cleaned.substring(0, 200));

                console.log('清洗后长度:', cleaned.length);

                // 第三步：使用Turndown.js进行HTML转Markdown
                console.log('使用Turndown.js进行HTML转Markdown转换...');

                if (window.TurndownService) {
                    try {
                        console.log('✅ Turndown.js可用，开始转换...');

                        // 创建Turndown服务实例，配置转换规则
                        const turndownService = new TurndownService({
                            headingStyle: 'atx', // 使用 # 格式的标题
                            hr: '---',
                            bulletListMarker: '-',
                            codeBlockStyle: 'fenced',
                            fence: '```',
                            emDelimiter: '*',
                            strongDelimiter: '**',
                            linkStyle: 'inlined'
                        });

                        // 添加自定义规则来处理Word特殊格式
                        turndownService.addRule('wordTitle', {
                            filter: function (node) {
                                // 检测Word标题格式：包含数字和点号/顿号的粗体文本
                                if (node.nodeName === 'P') {
                                    const text = node.textContent || '';
                                    const hasNumbering = /^\s*\d+[、.]/.test(text);
                                    const hasBold = node.querySelector('strong, b');
                                    return hasNumbering && hasBold;
                                }
                                return false;
                            },
                            replacement: function (content, node) {
                                // 清理内容，移除残留的样式信息
                                let cleanContent = content
                                    .replace(/^\s*[^>]*>\s*/, '') // 移除开头的残留标签内容
                                    .replace(/\s*<[^>]*>\s*$/, '') // 移除结尾的残留标签内容
                                    .replace(/[^"]*"\s*>\s*/, '') // 移除残留的属性和引号
                                    .replace(/^\s*[^a-zA-Z0-9\u4e00-\u9fa5]*/, '') // 移除开头的非文字字符
                                    .trim();

                                // 提取纯文本内容
                                const textContent = node.textContent || '';
                                const match = textContent.match(/(\d+[、.][\d.]*\s*[^\n\r]*)/);
                                if (match) {
                                    cleanContent = match[1].trim();
                                }

                                return '\n## ' + cleanContent + '\n\n';
                            }
                        });

                        // 添加规则清理残留的样式文本
                        turndownService.addRule('cleanStyleText', {
                            filter: function (node) {
                                if (node.nodeType === 3) { // 文本节点
                                    const text = node.textContent || '';
                                    return /^\s*[^a-zA-Z0-9\u4e00-\u9fa5]*[0-9.]+pt[^>]*>\s*/.test(text);
                                }
                                return false;
                            },
                            replacement: function (content) {
                                // 移除样式相关的文本
                                return '';
                            }
                        });

                        const markdown = turndownService.turndown(cleaned);

                        if (markdown && markdown.trim().length > 0) {
                            console.log('✅ Turndown.js转换成功，结果长度:', markdown.length);
                            return markdown;
                        } else {
                            console.log('⚠️ Turndown.js返回空结果');
                        }
                    } catch (e) {
                        console.warn('❌ Turndown.js转换失败:', e);
                    }
                } else {
                    console.log('❌ Turndown.js不可用，使用手动转换');
                }

                // 第四步：手动转换常见HTML标签为Markdown
                const manualResult = htmlToMarkdownManual(cleaned);

                // 第五步：验证转换结果
                if (manualResult && manualResult.trim().length > 0) {
                    console.log('手动转换成功，结果长度:', manualResult.length);
                    return manualResult;
                }

                // 第六步：如果所有转换都失败，尝试提取纯文本但保留基本结构
                console.log('所有转换失败，提取结构化文本');
                return extractStructuredText(cleaned, fallbackText);

            } catch (error) {
                console.error('HTML清洗过程出错:', error);
                return fallbackText || '';
            }
        }

        // 提取结构化文本的函数
        function extractStructuredText(html, fallbackText) {
            try {
                // 创建临时DOM元素来解析HTML
                const tempDiv = document.createElement('div');
                tempDiv.innerHTML = html;

                let result = '';

                // 递归处理所有节点
                function processNode(node) {
                    if (node.nodeType === 3) { // 文本节点
                        const text = node.textContent.trim();
                        if (text) {
                            result += text + ' ';
                        }
                    } else if (node.nodeType === 1) { // 元素节点
                        const tagName = node.tagName.toLowerCase();

                        // 处理不同的标签
                        switch (tagName) {
                            case 'h1':
                                result += '\n# ';
                                break;
                            case 'h2':
                                result += '\n## ';
                                break;
                            case 'h3':
                                result += '\n### ';
                                break;
                            case 'h4':
                                result += '\n#### ';
                                break;
                            case 'h5':
                                result += '\n##### ';
                                break;
                            case 'h6':
                                result += '\n###### ';
                                break;
                            case 'p':
                                result += '\n\n';
                                break;
                            case 'br':
                                result += '\n';
                                return; // br标签不需要处理子节点
                            case 'strong':
                            case 'b':
                                result += '**';
                                break;
                            case 'em':
                            case 'i':
                                result += '*';
                                break;
                            case 'li':
                                result += '\n- ';
                                break;
                            case 'blockquote':
                                result += '\n> ';
                                break;
                        }

                        // 递归处理子节点
                        for (let child of node.childNodes) {
                            processNode(child);
                        }

                        // 处理结束标签
                        switch (tagName) {
                            case 'h1':
                            case 'h2':
                            case 'h3':
                            case 'h4':
                            case 'h5':
                            case 'h6':
                                result += '\n';
                                break;
                            case 'strong':
                            case 'b':
                                result += '**';
                                break;
                            case 'em':
                            case 'i':
                                result += '*';
                                break;
                            case 'p':
                                result += '\n';
                                break;
                        }
                    }
                }

                processNode(tempDiv);

                // 清理结果
                result = result
                    .replace(/\n\s*\n\s*\n/g, '\n\n') // 移除多余的空行
                    .replace(/^\s+|\s+$/g, '') // 移除首尾空白
                    .trim();

                if (result && result.length > 0) {
                    console.log('结构化文本提取成功');
                    return result;
                }

                return fallbackText || '';
            } catch (error) {
                console.error('结构化文本提取失败:', error);
                return fallbackText || '';
            }
        }

        // 增强的HTML到Markdown转换函数
        function htmlToMarkdownManual(html) {
            console.log('开始手动转换，输入长度:', html.length);

            let result = html;

            // 第一步：预处理，移除嵌套的相同标签
            result = result
                // 移除嵌套的粗体标签
                .replace(/<(strong|b)[^>]*>\s*<(strong|b)[^>]*>(.*?)<\/\2>\s*<\/\1>/gi, '<$1>$3</$1>')
                // 移除嵌套的斜体标签
                .replace(/<(em|i)[^>]*>\s*<(em|i)[^>]*>(.*?)<\/\2>\s*<\/\1>/gi, '<$1>$3</$1>')
                // 清理空的格式标签
                .replace(/<(strong|b|em|i)[^>]*>\s*<\/\1>/gi, '');

            // 第二步：标题转换（优先处理，避免被其他规则影响）
            result = result
                .replace(/<h1[^>]*>(.*?)<\/h1>/gi, '\n# $1\n')
                .replace(/<h2[^>]*>(.*?)<\/h2>/gi, '\n## $1\n')
                .replace(/<h3[^>]*>(.*?)<\/h3>/gi, '\n### $1\n')
                .replace(/<h4[^>]*>(.*?)<\/h4>/gi, '\n#### $1\n')
                .replace(/<h5[^>]*>(.*?)<\/h5>/gi, '\n##### $1\n')
                .replace(/<h6[^>]*>(.*?)<\/h6>/gi, '\n###### $1\n');

            // 第三步：文本格式转换
            result = result
                // 粗体转换（合并strong和b标签）
                .replace(/<(strong|b)[^>]*>(.*?)<\/\1>/gi, '**$2**')
                // 斜体转换（合并em和i标签）
                .replace(/<(em|i)[^>]*>(.*?)<\/\1>/gi, '*$2*');

            // 第四步：结构元素转换
            result = result
                // 引用块（优先处理）
                .replace(/<blockquote[^>]*>(.*?)<\/blockquote>/gi, '\n> $1\n')
                // 列表项
                .replace(/<li[^>]*>(.*?)<\/li>/gi, '- $1\n')
                .replace(/<\/?(ul|ol)[^>]*>/gi, '\n')
                // 段落
                .replace(/<p[^>]*>(.*?)<\/p>/gi, '\n$1\n')
                // 换行
                .replace(/<br[^>]*>/gi, '\n')
                // 链接
                .replace(/<a[^>]*href="([^"]*)"[^>]*>(.*?)<\/a>/gi, '[$2]($1)')
                // 代码
                .replace(/<code[^>]*>(.*?)<\/code>/gi, '`$1`');

            // 第五步：移除剩余的HTML标签
            result = result.replace(/<[^>]*>/g, '');

            // 第六步：清理格式问题
            result = result
                // 修复重复的粗体标记
                .replace(/\*\*+/g, '**')
                .replace(/\*\*\s*\*\*/g, '**')
                // 修复重复的斜体标记
                .replace(/\*+(?!\*)/g, '*')
                .replace(/\*\s*\*(?!\*)/g, '*')
                // 修复空的格式标记
                .replace(/\*\*\s*\*\*/g, '')
                .replace(/\*\s*\*/g, '')
                // 清理多余的换行
                .replace(/\n\s*\n\s*\n/g, '\n\n')
                .replace(/\n\s+/g, '\n')
                // 清理首尾空白
                .replace(/^\s+|\s+$/g, '')
                .trim();

            console.log('手动转换完成，输出长度:', result.length);
            return result;
        }

        // 设置粘贴事件处理器
        function setupPasteHandler() {
            // 等待DOM更新
            setTimeout(() => {
                const contentArea = document.getElementById('contentArea');
                if (!contentArea) {
                    console.warn('contentArea未找到，无法设置粘贴处理器');
                    return;
                }

                // 查找Vditor的编辑区域
                const vditorTextarea = contentArea.querySelector('.vditor-textarea textarea') ||
                                     contentArea.querySelector('.vditor-wysiwyg') ||
                                     contentArea.querySelector('.vditor-ir') ||
                                     contentArea;

                console.log('找到编辑区域:', vditorTextarea);

                // 移除旧的事件监听器
                vditorTextarea.removeEventListener('paste', handlePaste);

                // 添加新的事件监听器
                vditorTextarea.addEventListener('paste', handlePaste, true);

                console.log('粘贴事件监听器已设置');
            }, 500);
        }

        // 粘贴事件处理函数
        function handlePaste(event) {
            console.log('--- 粘贴事件触发! ---');

            const clipboardData = event.clipboardData || window.clipboardData;
            if (!clipboardData) {
                console.warn('无法访问剪贴板数据');
                return;
            }

            const html = clipboardData.getData('text/html');
            const text = clipboardData.getData('text/plain');

            console.log('剪贴板HTML长度:', html ? html.length : 0);
            console.log('剪贴板文本长度:', text ? text.length : 0);

            // 如果没有HTML内容，让Vditor处理纯文本
            if (!html || html.trim().length === 0) {
                console.log('没有HTML内容，使用默认处理');
                return; // 让Vditor处理
            }

            // 阻止默认粘贴行为
            event.preventDefault();
            event.stopPropagation();

            console.log('原始HTML:', html.substring(0, 200) + '...');

            // 清洗HTML内容
            const cleanedContent = cleanHtmlContent(html, text);

            console.log('清洗后内容:', cleanedContent.substring(0, 200) + '...');

            // 插入清洗后的内容
            if (vditorInstance && typeof vditorInstance.insertValue === 'function') {
                vditorInstance.insertValue(cleanedContent);
                console.log('内容已插入编辑器');
            } else {
                console.error('无法插入内容，vditorInstance不可用');
            }
        }

        //编辑器核心功能
        /**
         * [增强版] 进入编辑器模式的函数
         * 该函数具有健壮的检查机制，并包含了增强的HTML清洗逻辑。
         */
        function enterEditMode() {
            isEditMode = true;
            $('#editBtn, #copyBtn, #uploadBtn, #deleteBtn').hide();
            $('#saveBtn, #cancelBtn').show();
            $('#contentArea').html(''); // 清空预览区，为 Vditor 提供一个干净的挂载点

            // 步骤 1: 健壮性检查
            // 在创建实例之前，先确认 Vditor 的全局对象和关键静态属性/方法是否都已准备就绪。
            // 这能确保我们不会在 Vditor 未完全加载时进行无效的调用。
            if (!window.Vditor || typeof window.Vditor !== 'function') {
                console.error('Vditor is not loaded or constructor is not available. Initialization aborted.');
                layer.msg('编辑器组件未加载，请刷新页面重试', {time: 5000});

                // 加载失败后，恢复按钮状态，避免界面卡死在“保存/取消”状态
                $('#editBtn, #copyBtn, #uploadBtn, #deleteBtn').show();
                $('#saveBtn, #cancelBtn').hide();
                isEditMode = false;

                return; // 中断函数执行
            }

            console.log('Vditor check passed. Version:', window.Vditor.version, '. Initializing editor...');

            // 步骤 2: 创建 Vditor 实例
            // 使用 window.Vditor 来明确表示我们用的是全局变量。
            vditorInstance = new window.Vditor('contentArea', {
                height: '100%',
                placeholder: '请在此处输入 Markdown 内容...',
                cache: { enable: false },
                value: currentMarkdownContent,
                mode: 'wysiwyg',
                preview: {
                    markdown: { sanitize: true }
                },
                upload: {
                    url: commonUrl.baseUrl + 'ddgc/ContentNewController/uploadImage',
                    headers: { 'Authorization': getCookie("token") || "" },
                    format(files, responseText) {
                        try {
                            const encryptedResponse = JSON.parse(responseText);
                            const decryptedText = sm4.decrypt(encryptedResponse.Data, '0123456789abcdeffedcba9876543210');
                            return decryptedText;
                        } catch (e) {
                            console.error("处理上传响应失败:", e);
                            layer.msg('客户端处理服务器响应失败！');
                            return JSON.stringify({ "msg": "Client-side error", "code": 1, "data": {} });
                        }
                    },
                },
                toolbar: [
                    'undo', 'redo', '|', 'bold', 'italic', 'strike', 'link', '|',
                    'check', 'outdent', 'indent', '|', 'quote', 'line', 'code',
                    'inline-code', 'insert-before', 'insert-after', '|', 'upload',
                    'table', '|', 'export'
                ],

                // 步骤 3: 添加初始化完成回调
                after: () => {
                    console.log('Vditor初始化完成，设置粘贴事件监听器');
                    setupPasteHandler();
                },
            });

            // 步骤 4: 更新UI
            $('#contentArea').addClass('vditor-container');
        }

        function exitEditMode(showConfirm = true) {
            var hasChanged = vditorInstance && vditorInstance.getValue() !== currentMarkdownContent;

            var doExit = function () {
                isEditMode = false;
                if (vditorInstance) {
                    vditorInstance.destroy();
                    vditorInstance = null;
                }
                $('#editBtn, #copyBtn, #uploadBtn, #deleteBtn').show();
                $('#saveBtn, #cancelBtn').hide();
                renderPreviewMode();
            };

            if (showConfirm && hasChanged) {
                layer.confirm('内容已修改，确定要取消吗？', {title: '提示'}, index => {
                    layer.close(index);
                    doExit();
                });
            } else {
                doExit();
            }
        }

        function saveContent() {
            console.log('开始保存内容, isEditMode:', isEditMode, 'vditorInstance:', !!vditorInstance);
            if (!isEditMode || !vditorInstance || typeof vditorInstance.getValue !== 'function') {
                console.error('无法保存：未处于编辑模式或 Vditor 实例无效');
                layer.msg('无法保存内容，请重新进入编辑模式');
                return;
            }

            var newContent;
            try {
                newContent = vditorInstance.getValue();
            } catch (error) {
                console.error('获取 Vditor 内容失败:', error);
                layer.msg('获取内容失败，请检查编辑器状态');
                return;
            }

            // 如果内容未更改，则不执行任何操作，直接退出编辑模式
            if (newContent === currentMarkdownContent) {
                console.log('内容未更改，跳过保存');
                layer.msg('内容未更改，无需保存');
                exitEditMode(false); // 退出编辑模式
                return;
            }

            // 1. 加载动画
            var loadingIndex = layer.load(2, {
                shade: [0.3, '#000'],
                time: 10 * 1000
            });

            // 准备 FormData
            var contentBlob = new Blob([newContent], {type: 'text/markdown'});
            var formData = new FormData();
            formData.append('file', contentBlob, 'online-edit.md');

            const parameter = {
                chapterId: currentChapterId,
                versionId: currentVersionId,
            };

            // SM4 加密
            try {
                let decData = sm4.encrypt(JSON.stringify(parameter), '0123456789abcdeffedcba9876543210');
                formData.append('parameter', decData);
            } catch (e) {
                console.error("加密参数时出错:", e);
                layer.msg("客户端参数加密失败！");
                layer.close(loadingIndex); // 加密失败时，关闭加载动画
                return;
            }

            console.log('发送保存请求，使用FormData方式');
            $.ajax({
                url: commonUrl.baseUrl + 'ddgc/ContentNewController/saveWithUpload',
                type: 'POST',
                data: formData,
                processData: false,
                contentType: false,
                headers: {
                    Authorization: getCookie("token") ? getCookie("token") : ""
                },
                success: function (res) {
                    console.log('保存响应:', res);
                    let parsedRes, decrypted, finalRes;
                    try {
                        parsedRes = typeof res === 'string' ? JSON.parse(res) : res;
                        decrypted = sm4.decrypt(parsedRes.Data, '0123456789abcdeffedcba9876543210');
                        finalRes = JSON.parse(decrypted);
                    } catch (e) {
                        console.error("解密或解析响应失败:", e);
                        layer.msg('处理服务器响应失败！');
                        return;
                    }

                    if (finalRes.code === 200) {
                        layer.msg('保存成功!');
                        currentMarkdownContent = newContent;
                        exitEditMode(false);
                    } else {
                        layer.msg('保存失败: ' + (finalRes.msg || '未知错误'));
                    }
                },
                error: function (xhr) {
                    console.error('保存内容错误:', xhr);
                    layer.msg('保存请求失败，请检查网络或稍后重试');
                },
                complete: function () {
                    layer.close(loadingIndex);
                }
            });
        }

        // 右上角按钮事件绑定
        $('#editBtn').on('click', enterEditMode);
        $('#saveBtn').on('click', saveContent);
        $('#cancelBtn').on('click', () => exitEditMode(true));
        $('#uploadBtn').on('click', () => {
            $('#hiddenFileInput').click();
        });
        $('#deleteBtn').on('click', () => {
            if (currentChapterId) deleteContent(currentChapterId, currentVersionId);
        });
        $('#copyBtn').on('click', () => {
            if (currentChapterId) openCopyPage();
        });

        //#exportBtn

        $("#exportBtn").on('click',function () {
            $ajax({
                type: "get",
                url: 'ddgc/ContentNewController/getMergeDocx',
                params: {
                    chapterId: currentChapterId,
                    versionId: currentVersionId
                }
            },function (res) {
                download(res)
            })
        })
        function download(obj) {
            debugger;
            var doc = encodeURI(obj.data.mergeDocx);
            const parameter = {
                s: doc,
                newName: obj.data.contentName
            };

            let updates = JSON.stringify(parameter)
            let decData = sm4.encrypt(updates, '0123456789abcdeffedcba9876543210')
            var subData = {}
            for(let key  in parameter){
                var datas = parameter[key]==undefined?'':parameter[key]
                datas= typeof datas=='number'?String(datas):datas
                subData[key]=sm4.encrypt(datas,'0123456789abcdeffedcba9876543210')
            }

            var encryptText = sm4.decrypt(subData, '0123456789abcdeffedcba9876543210')
            var encryptUp = sm3(encryptText)

            var url = 'ddgc/DownLoadController/downloadWord';
            let urlss = url.indexOf('?') > -1 ? (url + '&') : (url + '?')
            var ulrs = commonUrl.baseUrl + urlss + 'encrypt=' + encryptUp+'&s='+subData['s']+'&newName='+subData['newName']


            axios({
                method: 'get',
                url:  ulrs,
                headers: {
                    Authorization: getCookie("token") ? getCookie("token") : ""
                },
                responseType: 'blob'
            }).then(res => {
                let url = window.URL.createObjectURL(new Blob([res.data], {type: 'text/plain;charset=utf-8'}))
                let link = document.createElement("a")
                link.href = url;

                let filename = obj.data.contentName;
                link.setAttribute("download", filename)
                document.body.appendChild(link)
                link.click()
                document.body.removeChild(link)
            })
        }
        // 文件上传
        $('#hiddenFileInput').on('change', function () {
            if (this.files.length > 0) {
                layer.confirm(`确定要上传文件 "${this.files[0].name}" 吗？<br>这会覆盖当前章节的内容。`, {
                    title: '上传确认', btn: ['确定上传', '取消']
                }, index => {
                    layer.close(index);
                    submitUpload(currentChapterId, currentVersionId);
                });
            }
        });

        function submitUpload(chapterId, versionId) {
            var file = $('#hiddenFileInput')[0].files[0];
            var loadingIndex = layer.load(1, {shade: [0.3, '#000']});

            const parameter = {versionId: versionId, chapterId: chapterId, type: 1};
            let decData = sm4.encrypt(JSON.stringify(parameter), '0123456789abcdeffedcba9876543210');

            var subData = {data: decData};
            var encryptText = sm4.decrypt(subData.data, '0123456789abcdeffedcba9876543210');
            var encryptUp = sm3(encryptText);

            var formData = new FormData();
            formData.append('parameter', decData);
            formData.append('file', file);

            $.ajax({
                url: commonUrl.baseUrl + 'ddgc/ContentNewController/newUpload?encrypt=' + encryptUp,
                type: 'POST', data: formData, processData: false, contentType: false, timeout: 600000,
                headers: {Authorization: getCookie("token") || ""},
                success: function (res) {
                    layer.close(loadingIndex);
                    try {
                        let parsedRes = typeof res === 'string' ? JSON.parse(res) : res;
                        let decrypted = sm4.decrypt(parsedRes.Data, '0123456789abcdeffedcba9876543210');
                        let decryptedData = decrypted ? JSON.parse(decrypted) : {code: 500, msg: '解密失败'};
                        if (decryptedData.code === 200) {
                            layer.msg('上传成功');
                            getHtml(chapterId, versionId);
                        } else {
                            layer.msg('上传失败: ' + (decryptedData.msg || '未知错误'));
                        }
                    } catch (e) {
                        layer.msg('响应解析失败');
                    } finally {
                        $('#hiddenFileInput').val('');
                    }
                },
                error: function (xhr) {
                    layer.close(loadingIndex);
                    layer.msg('上传请求失败');
                    $('#hiddenFileInput').val('');
                },
                xhr: function () {
                    var xhr = new window.XMLHttpRequest();
                    xhr.upload.addEventListener("progress", evt => {
                        if (evt.lengthComputable) {
                            var percent = Math.round(evt.loaded / evt.total * 100);
                            layer.msg(percent < 100 ? `上传中: ${percent}%` : '处理中...', {time: 0});
                        }
                    }, false);
                    return xhr;
                }
            });
        }

        function deleteContent(chapterId, versionId) {
            layer.confirm('确定要删除该章节的内容吗？', {title: '删除确认', btn: ['确定', '取消']}, index => {
                layer.close(index);
                $ajax({url: 'ddgc/ContentNewController/delete', type: 'POST', params: {chapterId, versionId}}, res => {
                    if (res.code === 200) {
                        layer.msg('删除成功');
                        getHtml(chapterId, versionId);
                    } else {
                        layer.msg('删除失败: ' + (res.msg || '未知错误'));
                    }
                });
            });
        }

        function initCheckboxEvents() {
            $(document).off('change', '.chapter-checkbox').on('change', '.chapter-checkbox', function (e) {
                e.stopPropagation();
                const isChecked = $(this).prop('checked');
                const $item = $(this).closest('.menu-item, .sub-item');
                const $subMenu = $item.next('.sub-menu');
                if ($subMenu.length) {
                    $subMenu.find('.chapter-checkbox').prop('checked', isChecked);
                }
                updateParentCheckboxState($item);
                $('#batchDeleteBtn').toggle($('.chapter-checkbox:checked').length > 0);
            });

            $('#batchDeleteBtn').off('click').on('click', function () {
                const checkedItems = $('.chapter-checkbox:checked');
                if (checkedItems.length === 0) {
                    layer.msg('请选择要删除的章节');
                    return;
                }
                const chapterIds = checkedItems.map(function () {
                    return $(this).data('id');
                }).get();
                const versionIds = checkedItems.map(function () {
                    return $(this).data('version');
                }).get();
                layer.confirm(`确定要删除选中的 ${chapterIds.length} 个章节吗？`, {title: '批量删除确认'}, index => {
                    layer.close(index);
                    batchDeleteChapters(chapterIds, versionIds);
                });
            });
        }

        function batchDeleteChapters(chapterIds, versionIds) {
            // 将要删除的 chapterIds
            const deletedChapterIds = chapterIds.map(String);

            $ajax({
                url: 'ddgc/ContentNewController/batchDelete',
                type: 'POST',
                params: {
                    chapterIds: chapterIds.join(','),
                    versionIds: versionIds.join(',')
                },
            }, res => {
                if (res.code === 200) {

                    layer.msg(`成功删除 ${chapterIds.length} 个章节的内容`, {time: 2000});

                    $('#chapter-list .chapter-checkbox').prop('checked', false);
                    $('#batchDeleteBtn').hide();

                    const isCurrentContentDeleted = currentChapterId && deletedChapterIds.includes(String(currentChapterId));

                    if (isCurrentContentDeleted) {

                        $('#contentArea').html('<div id="default-prompt" style="text-align: center; margin-top: 20px; color: #888;"><h3>当前章节内容已被删除</h3></div>').removeClass('vditor-container');

                        currentMarkdownContent = '';

                        if (isEditMode) {
                            exitEditMode(false);
                        }
                    }
                } else {
                    layer.msg('批量删除失败: ' + (res.msg || '未知错误'));
                }
            });
        }

        function updateParentCheckboxState($item) {
            const $parentMenu = $item.closest('.sub-menu');
            if ($parentMenu.length) {
                const $parentItem = $parentMenu.prev('.menu-item, .sub-item');
                if ($parentItem.length) {
                    const $parentCheckbox = $parentItem.find('> .chapter-checkbox');
                    const $childCheckboxes = $parentMenu.find('> div > .chapter-checkbox');
                    const allChecked = $childCheckboxes.length > 0 && $childCheckboxes.length === $childCheckboxes.filter(':checked').length;
                    $parentCheckbox.prop('checked', allChecked);
                    updateParentCheckboxState($parentItem);
                }
            }
        }

        function openCopyPage() {
            layer.open({
                type: 2, title: '复制章节内容', area: ['98%', '98%'],
                content: 'copyPage.html?chapterId=' + currentChapterId + '&versionId=' + currentVersionId,
                success: (layero, index) => {
                    window.addEventListener('message', function (event) {
                        if (event.origin !== window.location.origin) return;
                        if (event.data.action === 'closeAndReturnData') {
                            if (event.data.data.success) {
                                layer.msg('复制成功！');
                                getHtml(currentChapterId, currentVersionId);
                            }
                            layer.close(index);
                        }
                    }, {once: true});
                }
            });
        }

        // 搜索功能
        $('#searchButton').on('click', performSearch);
        $('#searchInput').on('keypress', function (e) {
            // 回车键搜索功能
            if (e.which === 13) {
                performSearch();
            }
        });
        $('#resetButton').on('click', function () {
            // 退出搜索模式
            currentSearchKeyword = null;

            $('.menu-item, .sub-item').removeClass('search-highlight');
            $('#chapter-list .chapter').each(function () {
                $(this).html($(this).parent().attr('title'));
            });

            $('#searchInput').val("");

            // 如果当前有选中的章节，重新渲染它以清除高亮
            if (currentChapterId) {
                // currentSearchKeyword 为 null，render 不会高亮
                getHtml(currentChapterId, currentVersionId, null);
            } else {
                $('#contentArea').html('<div id="default-prompt" style="text-align: center; margin-top: 20px; color: #888;"><h3>请从左侧选择章节查看内容</h3></div>').removeClass('vditor-container');
            }
        });

        function performSearch() {
            const keyword = $('#searchInput').val().trim();

            if (!versionId || versionId === "0") {
                layer.msg('请先选择一个版本');
                return;
            }
            if (!keyword) {
                layer.msg('请输入搜索内容');
                return;
            }

            // 进入搜索模式，记录关键词
            currentSearchKeyword = keyword;

            if ($('.preview-area').length > 0) {
                renderPreviewMode(); // 清理旧的高亮
            }

            var loadingIndex = layer.load(2, {shade: [0.1, '#fff']});

            $ajax({
                type: "get",
                url: 'ddgc/ContentController/searchTree',
                params: {versionId: versionId, content: keyword}
            }, res => {
                layer.close(loadingIndex);
                if (res.code === 200 && res.data && Object.keys(res.data).length > 0) {
                    highlightSearchResults(res.data, keyword);

                    const $first = $('.menu-item.search-highlight, .sub-item.search-highlight').first();
                    if ($first.length) {
                        const chapterId = $first.data('target');
                        const versionId = $first.data('value');

                        $('.menu-item, .sub-item').removeClass('active');
                        $first.addClass('active');

                        if (isEditMode && vditorInstance && currentChapterId == chapterId) {
                            vditorInstance.search(keyword);
                            const searchResultEl = vditorInstance.vditor.element.querySelector('.vditor-search__current');
                            if (searchResultEl) {
                                searchResultEl.scrollIntoView({behavior: 'smooth', block: 'center'});
                            }
                        } else {
                            // 自动加载第一个结果，并传递关键词
                            getHtml(chapterId, versionId, currentSearchKeyword);
                        }
                    } else {
                        layer.msg('在章节标题中未找到匹配项。');
                    }
                } else {
                    // 搜索无结果，退出搜索模式
                    currentSearchKeyword = null;
                    // 清理可能存在的旧的搜索高亮
                    $('.menu-item, .sub-item').removeClass('search-highlight');
                    $('.chapter').each(function () {
                        $(this).html($(this).parent().attr('title'));
                    });
                    layer.msg(res.msg || '未找到匹配的章节');
                }
            }, err => {
                layer.close(loadingIndex);
                // 搜索失败，也应退出搜索模式
                currentSearchKeyword = null;
                layer.msg('搜索请求失败，请检查网络连接。');
            });
        }


        // 5. 高亮左侧目录
        function highlightSearchResults(results, keyword) {
            // 先清除所有旧的高亮
            $('.menu-item, .sub-item').removeClass('search-highlight');
            $('.chapter').each(function () {
                $(this).html($(this).parent().attr('title')); // 恢复原始标题
            });

            const regex = new RegExp(keyword.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'), 'gi');

            // 遍历所有目录项
            $('.menu-item, .sub-item').each(function () {
                const $item = $(this);
                const chapterId = $item.attr('data-target');

                if (results.hasOwnProperty(chapterId)) {
                    $item.addClass('search-highlight');

                    // 高亮章节标题中的关键词
                    const originalTitle = $item.attr('title');
                    const highlightedTitle = originalTitle.replace(regex, '<span class="highlight-text">$&</span>');
                    $item.find('.chapter').html(highlightedTitle);

                    // 展开该节点的所有父节点
                    expandNodePath($item);
                }
            });
        }

        function expandNodePath($node) {
            // 展开当前节点
            if (!$node.hasClass('expanded')) {
                $node.addClass('expanded');
                const $subMenu = $node.next('.sub-menu');
                $subMenu.css('max-height', $subMenu.prop('scrollHeight') + 'px');
            }

            const $parentSubMenu = $node.closest('.sub-menu');
            if ($parentSubMenu.length > 0) {
                const $parentItem = $parentSubMenu.prev('.menu-item, .sub-item');
                if ($parentItem.length > 0) {
                    expandNodePath($parentItem);
                }
            }
        }

    });
</script>
</body>
</html>
