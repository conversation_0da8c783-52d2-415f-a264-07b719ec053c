<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>粘贴清洗功能测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-area {
            border: 2px solid #ddd;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
        }
        .sample-content {
            background: #f5f5f5;
            padding: 15px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .result {
            background: #e8f5e8;
            padding: 15px;
            border-radius: 4px;
            margin: 10px 0;
            white-space: pre-wrap;
            font-family: monospace;
        }
        button {
            background: #007cba;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #005a87;
        }
    </style>
</head>
<body>
    <h1>HTML清洗功能测试</h1>
    
    <div class="test-area">
        <h2>测试样本</h2>
        <p>以下是一些包含复杂HTML格式的测试内容，您可以复制这些内容到编辑器中测试清洗效果：</p>
        
        <div class="sample-content">
            <h3>Word文档样式内容</h3>
            <p style="color: red; font-size: 14px; font-family: 'Times New Roman';">
                <strong>这是粗体文本</strong>，
                <em>这是斜体文本</em>，
                <span style="background-color: yellow;">这是高亮文本</span>
            </p>
            <ul>
                <li style="margin-left: 20px;">列表项目1</li>
                <li style="margin-left: 20px;">列表项目2</li>
            </ul>
        </div>
        
        <button onclick="copyToClipboard('sample1')">复制样本1</button>
        <button onclick="testCleanFunction()">测试清洗函数</button>
    </div>
    
    <div class="test-area">
        <h2>清洗结果预览</h2>
        <div id="result" class="result">点击"测试清洗函数"查看结果</div>
    </div>
    
    <div class="test-area">
        <h2>使用说明</h2>
        <ol>
            <li>点击"复制样本1"按钮复制测试内容</li>
            <li>在您的编辑器中粘贴内容</li>
            <li>观察是否正确清洗了HTML格式</li>
            <li>或者点击"测试清洗函数"直接查看清洗效果</li>
        </ol>
    </div>

    <script>
        // 复制HTML清洗函数（与主文件相同）
        function cleanHtmlContent(html, fallbackText) {
            try {
                let cleaned = html
                    .replace(/\s(class|style)="[^"]*"/gi, '')
                    .replace(/<o:p[\s\S]*?<\/o:p>/gi, '')
                    .replace(/<\/?\w+:[^>]*>/g, '')
                    .replace(/<!--[\s\S]*?-->/g, '')
                    .replace(/<(p|span|div|font)\b[^>]*>\s*<\/\1>/gi, '')
                    .replace(/\s(lang|xml:lang|xmlns|face|size|color)="[^"]*"/gi, '')
                    .replace(/\s+/g, ' ')
                    .trim();

                cleaned = htmlToMarkdownManual(cleaned);
                
                if (!cleaned || cleaned.trim().length === 0) {
                    return fallbackText || '';
                }

                return cleaned;
            } catch (error) {
                console.error('HTML清洗过程出错:', error);
                return fallbackText || '';
            }
        }

        function htmlToMarkdownManual(html) {
            return html
                .replace(/<h1[^>]*>(.*?)<\/h1>/gi, '\n# $1\n')
                .replace(/<h2[^>]*>(.*?)<\/h2>/gi, '\n## $1\n')
                .replace(/<h3[^>]*>(.*?)<\/h3>/gi, '\n### $1\n')
                .replace(/<h4[^>]*>(.*?)<\/h4>/gi, '\n#### $1\n')
                .replace(/<h5[^>]*>(.*?)<\/h5>/gi, '\n##### $1\n')
                .replace(/<h6[^>]*>(.*?)<\/h6>/gi, '\n###### $1\n')
                .replace(/<(strong|b)[^>]*>(.*?)<\/\1>/gi, '**$2**')
                .replace(/<(em|i)[^>]*>(.*?)<\/\1>/gi, '*$2*')
                .replace(/<p[^>]*>(.*?)<\/p>/gi, '\n$1\n')
                .replace(/<br[^>]*>/gi, '\n')
                .replace(/<li[^>]*>(.*?)<\/li>/gi, '- $1\n')
                .replace(/<\/?(ul|ol)[^>]*>/gi, '\n')
                .replace(/<a[^>]*href="([^"]*)"[^>]*>(.*?)<\/a>/gi, '[$2]($1)')
                .replace(/<code[^>]*>(.*?)<\/code>/gi, '`$1`')
                .replace(/<blockquote[^>]*>(.*?)<\/blockquote>/gi, '\n> $1\n')
                .replace(/<[^>]*>/g, '')
                .replace(/\n\s*\n\s*\n/g, '\n\n')
                .replace(/^\s+|\s+$/g, '')
                .trim();
        }

        function copyToClipboard(sampleId) {
            const sampleContent = `
                <p style="color: red; font-size: 14px; font-family: 'Times New Roman';">
                    <strong>这是粗体文本</strong>，
                    <em>这是斜体文本</em>，
                    <span style="background-color: yellow;">这是高亮文本</span>
                </p>
                <h2 style="color: blue;">这是一个标题</h2>
                <ul>
                    <li style="margin-left: 20px;">列表项目1</li>
                    <li style="margin-left: 20px;">列表项目2</li>
                </ul>
                <blockquote style="border-left: 3px solid #ccc; padding-left: 10px;">
                    这是一个引用块
                </blockquote>
            `;
            
            // 创建临时元素来复制HTML内容
            const tempDiv = document.createElement('div');
            tempDiv.innerHTML = sampleContent;
            document.body.appendChild(tempDiv);
            
            // 选择并复制
            const range = document.createRange();
            range.selectNodeContents(tempDiv);
            const selection = window.getSelection();
            selection.removeAllRanges();
            selection.addRange(range);
            
            try {
                document.execCommand('copy');
                alert('测试内容已复制到剪贴板！现在可以在编辑器中粘贴测试。');
            } catch (err) {
                alert('复制失败，请手动复制测试内容。');
            }
            
            // 清理
            selection.removeAllRanges();
            document.body.removeChild(tempDiv);
        }

        function testCleanFunction() {
            const testHtml = `
                <p style="color: red; font-size: 14px; font-family: 'Times New Roman';">
                    <strong>这是粗体文本</strong>，
                    <em>这是斜体文本</em>，
                    <span style="background-color: yellow;">这是高亮文本</span>
                </p>
                <h2 style="color: blue;">这是一个标题</h2>
                <ul>
                    <li style="margin-left: 20px;">列表项目1</li>
                    <li style="margin-left: 20px;">列表项目2</li>
                </ul>
                <blockquote style="border-left: 3px solid #ccc; padding-left: 10px;">
                    这是一个引用块
                </blockquote>
            `;
            
            const cleaned = cleanHtmlContent(testHtml, '纯文本后备');
            
            document.getElementById('result').innerHTML = 
                '原始HTML:\n' + testHtml + '\n\n' +
                '清洗后的Markdown:\n' + cleaned;
        }
    </script>
</body>
</html>
