<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Vditor版本和html2md测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-section {
            border: 1px solid #ddd;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
        }
        .result-area {
            background: #f5f5f5;
            padding: 15px;
            border-radius: 4px;
            margin: 10px 0;
            white-space: pre-wrap;
            font-family: monospace;
            max-height: 400px;
            overflow-y: auto;
        }
        button {
            background: #007cba;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #005a87;
        }
        .version-info {
            background: #e8f4fd;
            padding: 15px;
            border-radius: 4px;
            margin: 10px 0;
            border-left: 4px solid #007cba;
        }
    </style>
</head>
<body>
    <h1>Vditor版本和html2md方法测试</h1>
    
    <div class="test-section">
        <h2>🔍 当前版本信息</h2>
        <div id="currentVersion" class="version-info">正在检测当前Vditor版本...</div>
        <button onclick="checkCurrentVditor()">🔍 检查当前Vditor</button>
    </div>
    
    <div class="test-section">
        <h2>🧪 测试不同版本</h2>
        <p>尝试加载不同版本的Vditor来找到支持html2md的版本：</p>
        <button onclick="testVersion('3.11.1')">测试 v3.11.1 (当前)</button>
        <button onclick="testVersion('3.10.4')">测试 v3.10.4</button>
        <button onclick="testVersion('3.9.7')">测试 v3.9.7</button>
        <button onclick="testVersion('3.8.15')">测试 v3.8.15</button>
        <button onclick="testVersion('latest')">测试 latest</button>
    </div>
    
    <div class="test-section">
        <h2>📊 测试结果</h2>
        <div id="testResult" class="result-area">点击测试按钮查看结果...</div>
    </div>
    
    <div class="test-section">
        <h2>💡 解决方案</h2>
        <div class="version-info">
            <h3>可能的原因和解决方案：</h3>
            <ul>
                <li><strong>版本问题</strong>：某些版本的Vditor可能不包含html2md方法</li>
                <li><strong>构建问题</strong>：CDN版本可能是精简版，不包含所有功能</li>
                <li><strong>API变更</strong>：html2md方法可能在新版本中被重命名或移除</li>
                <li><strong>依赖问题</strong>：html2md可能需要额外的依赖库</li>
            </ul>
        </div>
    </div>

    <!-- 当前版本的Vditor -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/vditor@3.11.1/dist/index.css" />
    <script src="https://cdn.jsdelivr.net/npm/vditor@3.11.1/dist/index.min.js"></script>

    <script>
        function checkCurrentVditor() {
            let info = '=== 当前Vditor状态 ===\n\n';
            
            if (window.Vditor) {
                info += `✅ Vditor对象存在\n`;
                info += `📦 版本: ${window.Vditor.version || '未知'}\n`;
                info += `🔧 构造函数类型: ${typeof window.Vditor}\n\n`;
                
                // 检查所有可用的方法和属性
                const methods = Object.getOwnPropertyNames(window.Vditor);
                info += `📋 Vditor静态方法/属性 (${methods.length}个):\n`;
                methods.forEach(method => {
                    const type = typeof window.Vditor[method];
                    info += `  - ${method}: ${type}\n`;
                });
                
                // 特别检查html2md相关的方法
                info += `\n🎯 HTML转换相关方法:\n`;
                const htmlMethods = methods.filter(m => 
                    m.toLowerCase().includes('html') || 
                    m.toLowerCase().includes('md') || 
                    m.toLowerCase().includes('markdown') ||
                    m.toLowerCase().includes('convert')
                );
                
                if (htmlMethods.length > 0) {
                    htmlMethods.forEach(method => {
                        info += `  ✅ ${method}: ${typeof window.Vditor[method]}\n`;
                    });
                } else {
                    info += `  ❌ 未找到HTML转换相关方法\n`;
                }
                
                // 测试html2md方法
                info += `\n🧪 html2md方法测试:\n`;
                if (typeof window.Vditor.html2md === 'function') {
                    try {
                        const testHtml = '<h1>测试标题</h1><p><strong>粗体</strong>文本</p>';
                        const result = window.Vditor.html2md(testHtml);
                        info += `  ✅ html2md可用\n`;
                        info += `  📝 测试输入: ${testHtml}\n`;
                        info += `  📝 转换结果: ${result}\n`;
                    } catch (error) {
                        info += `  ❌ html2md调用失败: ${error.message}\n`;
                    }
                } else {
                    info += `  ❌ html2md方法不存在\n`;
                }
                
            } else {
                info += `❌ Vditor对象不存在\n`;
            }
            
            document.getElementById('currentVersion').innerHTML = info;
            document.getElementById('testResult').innerHTML = info;
        }

        function testVersion(version) {
            document.getElementById('testResult').innerHTML = `正在测试Vditor版本 ${version}...\n请稍候...`;
            
            // 移除当前的Vditor脚本
            const existingScripts = document.querySelectorAll('script[src*="vditor"]');
            existingScripts.forEach(script => script.remove());
            
            // 清除当前的Vditor对象
            if (window.Vditor) {
                delete window.Vditor;
            }
            
            // 加载新版本
            const script = document.createElement('script');
            script.src = `https://cdn.jsdelivr.net/npm/vditor@${version}/dist/index.min.js`;
            script.onload = () => {
                setTimeout(() => {
                    let result = `=== Vditor ${version} 测试结果 ===\n\n`;
                    
                    if (window.Vditor) {
                        result += `✅ 版本 ${version} 加载成功\n`;
                        result += `📦 实际版本: ${window.Vditor.version || '未知'}\n`;
                        result += `🔧 html2md方法: ${typeof window.Vditor.html2md}\n\n`;
                        
                        if (typeof window.Vditor.html2md === 'function') {
                            try {
                                const testHtml = '<h1>测试</h1><p><strong>粗体</strong></p>';
                                const markdown = window.Vditor.html2md(testHtml);
                                result += `🎉 html2md测试成功!\n`;
                                result += `📝 输入: ${testHtml}\n`;
                                result += `📝 输出: ${markdown}\n\n`;
                                result += `✅ 建议使用此版本！\n`;
                            } catch (error) {
                                result += `❌ html2md调用失败: ${error.message}\n`;
                            }
                        } else {
                            result += `❌ 此版本不包含html2md方法\n`;
                        }
                    } else {
                        result += `❌ 版本 ${version} 加载失败\n`;
                    }
                    
                    document.getElementById('testResult').innerHTML = result;
                }, 1000);
            };
            script.onerror = () => {
                document.getElementById('testResult').innerHTML = `❌ 无法加载Vditor版本 ${version}`;
            };
            
            document.head.appendChild(script);
        }

        // 页面加载完成后自动检查当前版本
        document.addEventListener('DOMContentLoaded', function() {
            setTimeout(checkCurrentVditor, 2000);
        });
    </script>
</body>
</html>
